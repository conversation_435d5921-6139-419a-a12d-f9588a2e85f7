CREATE TABLE IF NOT EXISTS public.sticky_messages (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY,
    guild_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    message_id BIGINT,
    name VARCHAR(100) NOT NULL,
    content TEXT,
    embed_data JSONB,
    view_data JSONB,
    enabled B<PERSON>OLEAN DEFAULT true,
    created_at BIGINT NOT NULL,
    updated_at BIGINT,
    created_by BIGINT NOT NULL,
    updated_by BIGINT,
    
    CONSTRAINT sticky_messages_pkey PRIMARY KEY (id),
    CONSTRAINT sticky_messages_guild_channel_name_unique UNIQUE (guild_id, channel_id, name)
);

-- Index for faster lookups
CREATE INDEX IF NOT EXISTS idx_sticky_messages_guild_channel 
ON sticky_messages (guild_id, channel_id) WHERE enabled = true;

CREATE INDEX IF NOT EXISTS idx_sticky_messages_message_id 
ON sticky_messages (message_id) WHERE message_id IS NOT NULL;
