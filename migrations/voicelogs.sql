-- In utils database
-- Main partitioned table using event_time (BIGINT)
CREATE TABLE IF NOT EXISTS public.voice_logs (
    log_id BIGINT GENERATED BY DEFAULT AS IDENTITY,
    guild_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    event_type TEXT NOT NULL,
    from_channel_id BIGINT,
    from_channel_name TEXT,
    to_channel_id BIGINT,
    to_channel_name TEXT,
    event_time BIGINT NOT NULL,
    event_timestamp TIMESTAMP WITH TIME ZONE GENERATED ALWAYS AS (to_timestamp(event_time)) STORED,
    PRIMARY KEY (log_id, event_time)
) PARTITION BY RANGE (event_time);

-- Default partition
CREATE TABLE voice_logs_default PARTITION OF voice_logs DEFAULT;

-- March 2025 partition (epoch: 1743465600 to 1746057600)
CREATE TABLE voice_logs_202503 PARTITION OF voice_logs
FOR VALUES FROM (1743465600) TO (1746057600);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_voice_logs_timestamp
ON voice_logs USING BRIN (event_timestamp) WITH (pages_per_range = 128);

CREATE INDEX IF NOT EXISTS idx_voice_logs_search
ON voice_logs USING GIN (to_tsvector('english',
    from_channel_name || ' ' || to_channel_name));

-- Performance indexes for vclogs queries
-- Index for user-specific queries (guild_id, user_id, event_time DESC)
CREATE INDEX IF NOT EXISTS idx_voice_logs_guild_user_time 
ON voice_logs (guild_id, user_id, event_time DESC);

-- Index for channel-specific queries (from_channel_id)
CREATE INDEX IF NOT EXISTS idx_voice_logs_guild_from_channel_time 
ON voice_logs (guild_id, from_channel_id, event_time DESC);

-- Index for channel-specific queries (to_channel_id)
CREATE INDEX IF NOT EXISTS idx_voice_logs_guild_to_channel_time 
ON voice_logs (guild_id, to_channel_id, event_time DESC);

-- Index for combined user+channel queries
CREATE INDEX IF NOT EXISTS idx_voice_logs_guild_user_channels_time 
ON voice_logs (guild_id, user_id, event_time DESC) 
WHERE from_channel_id IS NOT NULL OR to_channel_id IS NOT NULL;

-- Additional composite index for better channel OR queries
CREATE INDEX IF NOT EXISTS idx_voice_logs_guild_channels_time 
ON voice_logs (guild_id, event_time DESC) 
WHERE from_channel_id IS NOT NULL OR to_channel_id IS NOT NULL;
-- Automated cleanup procedure
CREATE OR REPLACE PROCEDURE voice_logs_maintenance()
LANGUAGE plpgsql AS $$
DECLARE
    retention_threshold BIGINT := EXTRACT(EPOCH FROM NOW() - INTERVAL '1 month')::BIGINT;
    next_month_start BIGINT := EXTRACT(EPOCH FROM date_trunc('month', NOW() + INTERVAL '1 month'))::BIGINT;
    next_month_end BIGINT := EXTRACT(EPOCH FROM date_trunc('month', NOW() + INTERVAL '2 month'))::BIGINT;
    partition_rec RECORD;
BEGIN
    -- Drop partitions older than 1 month
    FOR partition_rec IN
        SELECT
            child.relname AS partition_name,
            (regexp_match(pg_get_expr(child.relpartbound, child.oid), 'TO \((\d+)\)\)'))[1]::BIGINT AS upper_bound
        FROM pg_inherits
        JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
        JOIN pg_class child ON pg_inherits.inhrelid = child.oid
        WHERE parent.relname = 'voice_logs'
    LOOP
        IF partition_rec.upper_bound < retention_threshold THEN
            EXECUTE format('DROP TABLE %I', partition_rec.partition_name);
        END IF;
    END LOOP;

    -- Clean default partition (now 30-day threshold)
    EXECUTE 'DELETE FROM voice_logs_default WHERE event_time < $1'
    USING retention_threshold;

    -- Partition creation remains monthly
    IF NOT EXISTS (
        SELECT 1 FROM pg_partitions
        WHERE parentrelid = 'voice_logs'::regclass
        AND (regexp_match(pg_get_expr(relpartbound, oid), 'FROM \((\d+)\)'))[1]::BIGINT >= next_month_start
    ) THEN
        EXECUTE format(
            'CREATE TABLE voice_logs_%s PARTITION OF voice_logs
            FOR VALUES FROM (%s) TO (%s)',
            to_char(NOW() + INTERVAL '1 month', 'yyyymm'),
            next_month_start,
            next_month_end
        );
    END IF;
END;
$$;

-- In your defaultdb
CREATE EXTENSION IF NOT EXISTS pg_cron;

CREATE EXTENSION IF NOT EXISTS dblink;

-- Create a helper function to run maintenance in utils database
CREATE OR REPLACE FUNCTION run_utils_maintenance()
RETURNS void AS $$
BEGIN
    PERFORM dblink_connect(
        'utils_conn',
        'host=localhost dbname=utils user=your_user password=your_password'
    );

    PERFORM dblink_exec(
        'utils_conn',
        'CALL voice_logs_maintenance()'
    );

    PERFORM dblink_disconnect('utils_conn');
END;
$$ LANGUAGE plpgsql;

-- Schedule the cross-database maintenance
SELECT cron.schedule(
    'voice-logs-monthly',
    '0 3 1 * *',  -- Run at 3 AM on 1st of every month
    $$CALL voice_logs_maintenance()$$
);

SELECT *
FROM cron.job
WHERE jobname = 'voice-logs-monthly';