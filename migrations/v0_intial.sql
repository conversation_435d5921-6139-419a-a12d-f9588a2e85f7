CREATE TABLE IF NOT EXISTS public.guilds
(
    id bigint NOT NULL,
    prefix character varying COLLATE pg_catalog."default",
    mail jsonb,
    CONSTRAINT guilds_pkey PRIMARY KEY (id)
)

CREATE TABLE IF NOT EXISTS public.mails
(
    user_id bigint NOT NULL,
    channel_id bigint,
    guild_id bigint NOT NULL,
    local_id integer,
    dm boolean,
    claims bigint[],
    option text COLLATE pg_catalog."default",
    CONSTRAINT mails_pkey PRIMARY KEY (guild_id, user_id)
)

CREATE TABLE IF NOT EXISTS public.modlogs
(
    case_id integer NOT NULL DEFAULT nextval('modlogs_case_id_seq'::regclass),
    user_id bigint NOT NULL,
    action text COLLATE pg_catalog."default" NOT NULL,
    mod text COLLATE pg_catalog."default" NOT NULL,
    reason text COLLATE pg_catalog."default",
    guild_id bigint,
    mod_id bigint NOT NULL,
    "time" bigint,
    duration character varying COLLATE pg_catalog."default",
    CONSTRAINT modlogs_pkey PRIMARY KEY (case_id)
)