<!-- Timestamps: Content -->
dayjs.extend(window.dayjs_plugin_utc);
dayjs.extend(window.dayjs_plugin_timezone);
dayjs.extend(window.dayjs_plugin_customParseFormat);
dayjs.extend(window.dayjs_plugin_isToday);
dayjs.extend(window.dayjs_plugin_isTomorrow);
dayjs.extend(window.dayjs_plugin_isBetween);

dayjs.tz.setDefault("{{TIMEZONE}}")
dayjs().format("DD/MM/YYYY {{TIME_FORMAT}}");
var timeStamps = document.getElementsByClassName('chatlog__timestamp');
for(var i = 0; i < timeStamps.length; i++) {
    const date_1 = dayjs.tz(timeStamps[i].innerText, "DD-MM-YYYY {{TIME_FORMAT}}", "{{TIMEZONE}}");
    const date_2 = dayjs.tz();
    const diff = date_2.diff(date_1, 'day', true)

    if (date_1.isTomorrow()) {
        timeStamps[i].ineerText = "Tomorrow at " + date_1.format('{{TIME_FORMAT}}')
    } else if (date_1.isToday()) {
        timeStamps[i].innerText = "Today at " + date_1.format('{{TIME_FORMAT}}')
    } else if (date_1.add(1, 'day').isToday()) {
        timeStamps[i].innerText = "Yesterday at " + date_1.format('{{TIME_FORMAT}}')
    } else if (date_1.isBetween(date_2, date_2.subtract(7, 'day'))) {
        timeStamps[i].innerText = date_1.day(date_1.day()).format("dddd [at] {{TIME_FORMAT}}")
    }
}