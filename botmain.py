import discord
from discord.ext import commands
import asyncpg
from dotenv import load_dotenv
import os
import json

load_dotenv()

class MyBot(commands.Bot):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    async def setup_hook(self):
        
        async def _setup_connection(con):
                    await con.set_type_codec('jsonb', schema='pg_catalog',
                                            encoder= json.dumps , decoder=json.loads)

        self.db = await asyncpg.create_pool(dsn= os.environ.get("DB") ,  init=_setup_connection)
        print("Connection to db DONE!")
        guilds = await self.db.fetch("SELECT * FROM guilds")
        self.data = { guild['id'] : dict(guild) for guild in guilds }
        
        # app command settings

        self.app_command_settings = {1325171820592238592 : { 'default' : { 'channel' : { 'allowed' : set() , 'denied' : set() , 'all_channels' : True } , 'role' : { 'allowed' : set() , 'denied' : set() , 'everyone' : True} } } , 
                                     848588208165748737 : { 'default' : { 'channel' : { 'allowed' : set() , 'denied' : set() , 'all_channels' : True } , 'role' : { 'allowed' : set() , 'denied' : set() , 'everyone' : True} } } }
        self.app_command_names  = {}
        #load Files
        
        for filename in os.listdir('./commands'):
            if filename.endswith('.py'):
                await client.load_extension( f'commands.{filename[:-3]}')
            elif not filename.endswith('.py'):
                filenametemp =  filename
                for filename in os.listdir(f'./commands/{filenametemp}'):
                    if filename.endswith('.py'):
                        await client.load_extension(f'commands.{filenametemp}.{filename[:-3]}')
        
defult_prefix = "!"

async def get_prefix(client , message):  
    try :
        prefix = client.data[message.guild.id]['prefix']
        if not prefix :
            raise Exception
    except Exception:
        prefix = defult_prefix
    finally :
        return commands.when_mentioned_or(prefix)(client , message)

# intents = discord.Intents.all() 
intents = discord.Intents.default()
intents.members = True
intents.message_content = True

client = MyBot( command_prefix =  get_prefix , strip_after_prefix =True, case_insensitive=True, intents=intents , help_command=None )

embed_color = 0x2b2d31

class Confirm(discord.ui.View):
    def __init__(self , user = None , role = None):
        super().__init__()
        self.value = None
        self.user = user
        self.role = role

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.green)
    async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.user and self.user != interaction.user :
            await interaction.response.send_message('Not your interaction', ephemeral=True)
            return
        if self.role and self.role not in interaction.user.roles :
            await interaction.response.send_message('Not your interaction', ephemeral=True)
            return
        await interaction.response.send_message('Confirming', ephemeral=True)
        self.value = True
        self.stop()

    @discord.ui.button(label='Cancel', style=discord.ButtonStyle.grey)
    async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.user and self.user != interaction.user :
            await interaction.response.send_message('Not your interaction', ephemeral=True)
            return
        if self.role and self.role not in interaction.user.roles :
            await interaction.response.send_message('Not your interaction', ephemeral=True)
            return
        await interaction.response.send_message('Cancelling', ephemeral=True)
        self.value = False
        self.stop()

class SingleInput(discord.ui.Modal, title='...'):
    def __init__(self, question, placeholder):
        super().__init__()
        self.question = question
        self.placeholder = placeholder
        self.value = None
        self.input = discord.ui.TextInput(
            label=self.question, placeholder=self.placeholder)
        self.add_item(self.input)

    async def on_submit(self, interaction: discord.Interaction):
        self.value = self.input.value
        await interaction.response.defer()

def bembed(message=None) :
    return discord.Embed( description= message , color= 0x2b2d31 )

def guild_prefix(guild_id : int):
    return client.data[guild_id]['prefix'] or defult_prefix

def check_app_command_permission( ctx):
    # if ctx.interaction :
    #     return True
    if ctx.guild.id not in client.app_command_settings:
        return False
    if ctx.command.name not in client.app_command_settings[ctx.guild.id]:
        return False
    
    if ctx.author.guild_permissions.manage_guild:
        return True

    perms = client.app_command_settings[ctx.guild.id][ctx.command.name]
    if perms['channel']['all_channels'] == True and ctx.channel.id in perms['channel']['denied']:
        return False
    elif perms['channel']['all_channels'] == False and ctx.channel.id not in perms['channel']['allowed']:
        return False 
    if perms['role']['everyone'] == False :
        user_roles = set([role.id for role in ctx.author.roles])
        # find common roles in user_roles and allowed roles
        common = user_roles.intersection(perms['role']['allowed'])
        if not common :
            if ctx.author.id not in perms['user']['allowed']:
                return False
    
    if ctx.author.id in perms['user']['denied']:
        return False

    return True
