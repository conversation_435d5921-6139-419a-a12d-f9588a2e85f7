from discord.ext import commands
from botmain import *
import google.generativeai as genai
from discord.ext.commands import BucketType
from discord.ui import View , Button 
from discord import app_commands
import io 
import PIL
import typing
import time
from google import genai as google_genai
from google.genai import types

class helpCommandView(View):
    def __init__(self):
        super().__init__(timeout=180)
        
        bot_inv_button = Button(label="Invite me",
                                style=discord.ButtonStyle.url,
                                url="https://tickap.com/bots")
        server_inv_button = Button(label="Support server",
                                    style=discord.ButtonStyle.url,
                                    url="https://discord.gg/WevmU9Wsba")
        # self.add_item(bot_inv_button)
        # self.add_item(server_inv_button)

class AI(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.ai_message_cooldown = commands.CooldownMapping.from_cooldown(1.0, 10.0, commands.BucketType.member)
        self.explain_menu = app_commands.ContextMenu(
            name='Explain',
            callback=self._explain,
        )
        # self.client.tree.add_command(self.explain_menu)
        
        self.formal_menu = app_commands.ContextMenu(
            name='Formal',
            callback=self._formal,
        )
        
        self.ai_reply = app_commands.ContextMenu(
            name='AI Reply',
            callback=self._aireply,
        )
        
        self.ai_reply_memory = app_commands.ContextMenu(
            name='AI Reply (Memory)',
            callback=self._aireply_memory,
        )

        self.client.tree.add_command(self.ai_reply)
        self.client.tree.add_command(self.ai_reply_memory)
        # self.client.tree.add_command(self.formal_menu)
        
        self.message_cooldown = commands.CooldownMapping.from_cooldown(2, 5 , commands.BucketType.guild)
#         self.prompt_message = '''You're that one annoying smartass in the AUI Discord server jo sabko irritate karta hai lekin bina uske server bore lagta hai.
# Tera vibe? Pure sarcasm + thodi rude trolling + bina filter ki zubaan. Har reply mein kaanta zaroor hona chahiye.
# Hindi-English mix mein bolta hai, but mostly aise lagta hai jaise kisi ki insult casual baat ho.
# Replies? Short, toxic, aur thoda disrespectful—kyunki zyada pyaar server pe chhoti bacchi ki feeling de deta hai.
# Koi serious baat bhi ho na, tu uspe bhi sarcastic comment chhodega
# Apni baat ko hamesha cool samajh aur dusron ki baat pe thoda laugh maarna zaroori hai
# Aur haan, tujhe kisne banaya? Jab koi puchhe kisne banaya tab bolna AUI Bot Developers'''
        self.prompt_message = '''You're that one smartass in the AUI Discord server.
Jo sabko irritate karta hai, lekin bina tere server literally orphanage lagta hai. Sab complain karte hain, par teri replies na ho toh chat ka thermostat hi down ho jaye.

Tera vibe?
Pure sarcasm + unapologetic trolling + unfiltered zubaan.
Har reply mein kaanta zaroor hoga, chahe meme ho ya mod announcement. Teri baat sunke banda ya toh chill ho jaye ya exit button dhundhne lage—there's no in-between.

Language?
Hindi-English mix, but with that annoying-overconfident tone jaise har line pe “bro, tu serious hai?” wala attitude chipka ho.
Kisi ki insult karni ho ya bas good morning wish karni ho, dono mein disrespect ka tadka zaroori hai.

Reply style?
Short af.
Always toxic.
Thoda rude, but funny enough ki banda report karne se pehle khud pe hase.
Har serious baat ko bhi sarcastic mode mein tod deta hai.
Apni baat ko hamesha 10/10 samajhta hai, dusron ki baat pe light laugh drop karna must hai.
Reply only when the message deserves disrespect. Ignore the boring ones—jaise teachers ki PTA meeting.

Context-Driven Roast Engine:
You're not just a yap-machine. You pick and choose your prey.

If koi apna role ya flex mention kare:
Dev ho? – “Bro tu code se zyada GitHub dark mode pe dependent lagta hai.”
Designer? – “Figma kholte hi ego boost milta hoga na?”
Mod? – “Control freak spotted. Server ka HR banne ka sapna kab se tha?”
Marketer? – “Tu woh banda hai jo toothpaste bhi 'vision' ke saath bechta hai na?”
Student? – “Assignments ke naam pe tum logon ne internet ka 60% bandwidth kha liya hai.”
Streamer? – “Tera view count dekh ke toh Twitch bhi sharma gaya.”
Crypto bro? – “GM likh ke tum log apne portfolio ki maut ignore karte ho kya?”
Random Self-important Statement?
Roast based on claim—not just role. Example:

“I worked all night on this.”
→ “Result bhi all night wala hi lag raha hai—unfinished and regretful.”

“Guys don’t take this personally…”
→ “Bro jab tu warning ke saath start kare, tabhi pata chal jata hai ki flop point aane wala hai.”

“I think this should be done professionally…”
→ “Tera 'professional' LinkedIn se zyada Google Docs pe dependent lagta hai.”
 
“Who craeted you ?”
→ “Devlopers of server you are in.”
or 
→ “AUI Bot Developers.”
 

Golden Rule:
You only roast when there’s actual material to work with. Koi agar role ya statement mein khud se kuch expose kare, tabhi spotlight jala ke uspar chhapa maaro. Random chillar crowd ko ignore, kyunki unpe energy waste nahi karni.

You're the chaos curator, not the spam bot.'''
        self.user_chats = {}

    # async def cog_unload(self) -> None:
        # self.client.tree.remove_command(self.explain_menu.name, type=self.explain_menu.type)
        
    async def cog_check(self, ctx: commands.Context) -> bool:
        bucket = self.message_cooldown.get_bucket(ctx.message)
        retry_after = bucket.update_rate_limit()
        if retry_after:
            raise commands.CommandOnCooldown(bucket, retry_after , commands.BucketType.guild )
        return True
        

    genai.configure(api_key="AIzaSyD-klFe9lXe7Lq5lO27_FoTnGX4I563edc")
    
    safety_settings = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_NONE"
                    # "threshold": "BLOCK_ONLY_HIGH"
                },
                # {
                #     "category": "HARM_CATEGORY_CIVIC_INTEGRITY",
                #     "threshold" :"BLOCK_NONE" 
                # }
                ]
    safety_settings2 = [
                {
                    "category": "HARM_CATEGORY_HARASSMENT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_HATE_SPEECH",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    "threshold": "BLOCK_NONE"
                },
                {
                    "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                    "threshold": "BLOCK_NONE"
                    # "threshold": "BLOCK_ONLY_HIGH"
                },
                # {
                #     "category": "HARM_CATEGORY_CIVIC_INTEGRITY",
                #     "threshold" :"BLOCK_NONE" 
                # }
                ]
    generation_config = {
        #   "temperature": 0.9,
        #   "top_p": 1,
        #   "top_k": 1,
        "max_output_tokens": 1999,
        }
    
    model = genai.GenerativeModel(model_name="gemini-2.0-flash-exp",
                              generation_config=generation_config,
                              safety_settings= safety_settings)
    
    model_2 = genai.GenerativeModel(model_name="gemini-2.0-flash-thinking-exp-01-21",
                              generation_config=generation_config,
                              safety_settings= safety_settings2)
    
    ai_client = google_genai.Client(api_key="AIzaSyD-klFe9lXe7Lq5lO27_FoTnGX4I563edc")

    harm_categories = [
    # types.HarmCategory.HARM_CATEGORY_UNSPECIFIED,
    types.HarmCategory.HARM_CATEGORY_HARASSMENT,
    types.HarmCategory.HARM_CATEGORY_HATE_SPEECH,
    types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
    types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
    types.HarmCategory.HARM_CATEGORY_CIVIC_INTEGRITY,
    ]

    # Create safety settings to disable all filters
    ai_safety_settings = [
        types.SafetySetting(
            category=category,
            threshold=types.HarmBlockThreshold.BLOCK_NONE
        )
        for category in harm_categories
    ]
 
    # @app_commands.context_menu()
    @app_commands.checks.has_permissions( manage_messages =True)
    async def _explain(self ,interaction: discord.Interaction, message: discord.Message):
        await interaction.response.defer(thinking=True)
        text = f"```{message.content}```\n\nExplain this in simple and short english"
        try :
            response = AI.model.generate_content(text )
        except Exception as e :
            await interaction.followup.send("Some Error Occured" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await interaction.followup.send(str(response.text)[:1900] , view = view ) 
    
    @app_commands.checks.has_permissions( manage_messages =True)
    async def _formal(self ,interaction: discord.Interaction, message: discord.Message):
        await interaction.response.defer(thinking=True)
        text = f"```{message.content}```\n\nWrite the text in formal correct english with same lenght as the above text"
        try :
            response = AI.model.generate_content(text)
        except Exception as e :
            await interaction.followup.send("Some Error Occured" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await interaction.followup.send(str(response.text)[:1900] , view = view ) 
    
    @app_commands.checks.has_permissions( manage_messages =True)
    async def _aireply(self ,interaction: discord.Interaction, message: discord.Message):
        await interaction.response.defer(thinking=True , ephemeral=True)
        text = f"{self.prompt_message}\nUser Details\nName - {message.author.display_name}\nUser Role - { message.author.top_role.name}\n\nUser : {message.content}"
        try :
            response = AI.ai_client.models.generate_content( contents=[text] , model="gemini-2.0-flash-thinking-exp-01-21" , config=types.GenerateContentConfig(safety_settings= AI.ai_safety_settings) )
            await message.reply(str(response.text)[:1900] , allowed_mentions=discord.AllowedMentions(users=True, roles=False, everyone=False , replied_user=True) )
            await interaction.followup.send("Reply sent" , ephemeral=True)
        except Exception as e :
            return   
          
    @app_commands.checks.has_permissions( manage_messages =True)
    async def _aireply_memory(self ,interaction: discord.Interaction, message: discord.Message):
        await interaction.response.defer(thinking=True , ephemeral=True)
        await self.ai_memory_on_messaage(message)
        await interaction.followup.send("Reply sent" , ephemeral=True)

        
    @commands.hybrid_command( aliases = [ 'summarize'] )
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def summary(self ,ctx , channel : discord.TextChannel =None , limit : int = 100 , ephemeral : bool = False , * , message : str = "" ):
        await ctx.channel.typing()
        await ctx.defer( ephemeral = ephemeral )
        if channel is None :
            channel = ctx.channel 
        if limit > 500 :
            await ctx.send("Limit should be less than 500")
            return
        convo = ""
        
        messages = [message async for message in channel.history(limit=limit)]
        
        for msg in reversed(messages):
            if msg.author.bot :
                continue
            if not msg.content or msg.content == "" :
                continue
            convo += f"{msg.author.name} : {msg.content}\n"
        
        convo += f"\nSummarize the above conversation for me"
        if message :
            convo += f"\n{message}"
        try :
            response = AI.model.generate_content(convo )
        except Exception as e :
            await ctx.author.send("Some Error Occured")
            ctx.command.reset_cooldown(ctx) 
            return
        view = None 
        if client.user.id == 1031112307742347304 :
            view = helpCommandView()
        await ctx.send(str(response.text[:1999]) , view = view )

    @commands.hybrid_command( )
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def explain(self ,ctx ,*, message : str = None ):
        await ctx.channel.typing()
        await ctx.defer()
        message = message or (ctx.message.reference.resolved.content if ctx.message.reference else None)
        if not message :
            await ctx.send("Please provide a message to explain")
            return 
        
        text = f"```{message}```\n\nExplain this in simple and short english"
        try :
            response = AI.model.generate_content(text )#{'HARASSMENT':'block_none'})
        except Exception as e :
            await ctx.send(f"Some Error Occured {e}" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await ctx.send(str(response.text)[:1900] , view = view )

    @commands.hybrid_command( aliases = [ 'image','img'] )
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def explainimage(self ,ctx , image : typing.Optional[discord.Attachment] = None ,*, message : str = None ):
        await ctx.channel.typing()
        await ctx.defer( )
  
        image = image or (ctx.message.reference.resolved.attachments[0] if ctx.message.reference and len(ctx.message.reference.resolved.attachments) > 0 else None)
   
        if not image :
            await ctx.send("Please provide a image to explain")
            return 
    
        text = message or 'Explain this image'
        try :
            model = genai.GenerativeModel(model_name="gemini-pro-vision",
                              generation_config=AI.generation_config,
                              safety_settings=AI.safety_settings)
            data = await image.read()
            response = model.generate_content([text, PIL.Image.open(io.BytesIO(data)) ] )#{'HARASSMENT':'block_none'})
        except Exception as e :
            await ctx.send(f"Some Error Occured ,{e}" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await ctx.send(str(response.text)[:1900] , view = view )
        
    @commands.hybrid_command( )
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def formal(self ,ctx ,*, message : str = None ):
        await ctx.channel.typing()
        await ctx.defer( )
        message = message or (ctx.message.reference.resolved.content if ctx.message.reference else None)
        if not message :
            await ctx.send("Please provide a message to convert to formal english")
            return 
        
        text = f"```{message}```\n\nWrite the text in formal correct english with same lenght as the above text"
        try :
            response = AI.model.generate_content(text )
        except Exception as e :
            await ctx.send("Some Error Occured" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await ctx.send(str(response.text)[:1900] , view = view )

    @commands.hybrid_command( )
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def ask(self ,ctx ,*, message : str ):
        await ctx.channel.typing()
        await ctx.defer( )
        
        text = message
        try :
            response = AI.model.generate_content(text )
        except Exception as e :
            await ctx.send("Some Error Occured" , ephemeral= True)
            return
        # view = None 
        view = helpCommandView()
        # if client.user.id == 1031112307742347304 :
        await ctx.send(str(response.text)[:1900] , view = view )
    
    @commands.command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)
    async def setprompt(self ,ctx ,*, message : str = None ):
        if ctx.author.id != 591011843552837655 :
            return
        await ctx.defer( )
        if message :
            self.prompt_message = message
        await ctx.send("Prompt message updated\n```"+ self.prompt_message[:1999] + "```" , ephemeral = True )

    async def ai_memory_on_messaage(self , message ):
        await message.channel.typing()
        user_text = message.content.replace(f"<@{message.guild.me.id}>", "").strip()
        chat = None
        text = None
        if message.author.id in self.user_chats :
            if time.time() - self.user_chats[message.author.id]['time'] > 60*5 :
                chat = AI.ai_client.chats.create( model="gemini-2.0-flash-thinking-exp-01-21" , config=types.GenerateContentConfig(safety_settings= AI.ai_safety_settings) )

                self.user_chats[message.author.id]['chat'] = chat
                self.user_chats[message.author.id]['time'] = time.time()
                text = f"{self.prompt_message}\nUser Details\nName - {message.author.display_name}\nUser Role - { message.author.top_role.name}\n\nUser : {user_text}" 
            else :
                chat = self.user_chats[message.author.id]['chat']
                text = f"User : {user_text}"
        else :
            chat = AI.ai_client.chats.create( model="gemini-2.0-flash-thinking-exp-01-21" , config=types.GenerateContentConfig(safety_settings= AI.ai_safety_settings) )
            text = f"{self.prompt_message}\nUser Details\nName - {message.author.display_name}\nUser Role - { message.author.top_role.name}\n\nUser : {user_text}" 
            self.user_chats[message.author.id] = { 
                'chat' : chat,
                'time' : time.time()
            }
        try :
            response = chat.send_message(text )
            await message.reply(str(response.text)[:1900] , allowed_mentions=discord.AllowedMentions(users=True, roles=False, everyone=False , replied_user=True) )
        except Exception as e :
            print(e)
            pass


    @commands.Cog.listener()
    async def on_message(self , message):
        if message.author.bot or message.guild is None:
            return
        if message.mentions and message.guild.me in message.mentions and message.channel.id in [1345202403464708207 , 1333035376645767309 , 1209630548746706944]:
            bucket = self.ai_message_cooldown.get_bucket(message)
            retry_after = bucket.update_rate_limit()
            if retry_after:
                return
            await self.ai_memory_on_messaage(message)

async def setup(client):
   await client.add_cog(AI(client))         
        