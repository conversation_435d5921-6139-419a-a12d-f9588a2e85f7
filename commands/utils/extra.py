
import discord
from discord.ext import commands
from botmain import *
import typing

class Extra(commands.Cog):

    def __init__(self , client):
        self.client = client
        
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def mvr(self ,ctx , member : typing.Optional[discord.Member], * ,vc : str ):
        '''
        Permission of this command is managed from server integrations
        '''
        if member is None:
            member = ctx.message.reference.resolved.author if ctx.message.reference else None
            if member is None:
                return await ctx.send("No member found")
        
        if not member.voice:
            return await ctx.send("Member is not in a voice channel")
        
        channels = {(channel.name).lower() : channel for channel in ctx.guild.voice_channels if channel.category_id in [ 1319054877174796370 , 1319045925057859625 , 1319052197656268882 , 1319052780308271125 , 1319053654933766144 , 1319189724434403349 , 1319158849579515985 ] }

        def find_match(abbreviated, words):
            for word in words:
                if all(char in word for char in abbreviated) and all(word.index(abbreviated[i]) <= word.index(abbreviated[i + 1]) for i in range(len(abbreviated) - 1)):
                    return word
            return None

        match = find_match(vc.lower(),[ channel for channel in channels if channel.startswith(vc[0])])
        if match is None:
            return await ctx.send("No vc found")
        view = Confirm(member)
                
        await ctx.send(f"{member.mention} , {ctx.author.mention} wants to move you to {channels[match].mention}", view=view)
        await view.wait()
        if view.value:
            try :
                before_vc = member.voice.channel 
                await member.move_to(channels[match])
                # await ctx.guild.get_channel(1153463862205632584).send( embed = discord.Embed( description=f"**User** {member.mention}\n**From** {before_vc.mention} **To** {channels[match].mention}\n**Responsible User** {ctx.author.mention} `{ctx.author.id}`" , color = 0x2b2c31 ).set_author(name="MoveVc Req Log", icon_url=ctx.guild.icon) )
            except :
                pass
            
    @commands.hybrid_command( aliases = ["rp"])
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def roleping(self , ctx , role : str , * , message : str = None):
        '''
        Permission of this command is managed from server integrations
        '''
        if not ctx.interaction:
            await ctx.message.delete()
        roles = { (role.name).lower() : role for role in ctx.guild.roles if role.name[-2:] == "MM"}
        # role.sort()
        def find_match(abbreviated, words):
            for word in words:
                if all(char in word for char in abbreviated) and all(word.index(abbreviated[i]) <= word.index(abbreviated[i + 1]) for i in range(len(abbreviated) - 1)):
                    return word
            return None
        
        match = find_match(role.lower(), sorted([ r for r in roles if r.startswith(role[0])]))
        await ctx.send(f"{roles[match].mention} , {message or ' '}")
        
    @commands.hybrid_command( aliases = ["art", "rart"])
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def artist( self , ctx , member : discord.Member ):
        artist_role = discord.utils.get(ctx.guild.roles , name = "Artist")
        if ctx.invoked_with == "art" :
            if artist_role not in member.roles:
                await member.add_roles(artist_role)
            await ctx.send(f"{member.mention} is now an Artist")
        elif ctx.invoked_with == "rart" :
            if artist_role in member.roles:
                await member.remove_roles(artist_role)
            await ctx.send(f"{member.mention} is no longer an Artist")
        else :
            if artist_role in member.roles:
                await member.remove_roles(artist_role)
                await ctx.send(f"{member.mention} is no longer an Artist")
            else :
                await member.add_roles(artist_role)
                await ctx.send(f"{member.mention} is now an Artist")

# create a similer command for Melodist

    @commands.hybrid_command(aliases=["am" , "rm"])
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def melodist(self , ctx , member : discord.Member ):
        melodist_role = discord.utils.get(ctx.guild.roles , name = "Melodist")
        if ctx.invoked_with == "am" :
            if melodist_role not in member.roles:
                await member.add_roles(melodist_role)
            await ctx.send(f"{member.mention} is now a Melodist")
        elif ctx.invoked_with == "rm" :
            if melodist_role in member.roles:
                await member.remove_roles(melodist_role)
            await ctx.send(f"{member.mention} is no longer a Melodist")
        else :
            if melodist_role in member.roles:
                await member.remove_roles(melodist_role)
                await ctx.send(f"{member.mention} is no longer a Melodist")
            else :
                await member.add_roles(melodist_role)
                await ctx.send(f"{member.mention} is now a Melodist")

# Gambler

    @commands.hybrid_command(aliases=["ag" , "rg"])
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def gambler(self , ctx , member : discord.Member ):
        gambler_role = discord.utils.get(ctx.guild.roles , name = "Gambler")
        if ctx.invoked_with == "ag" :
            if gambler_role not in member.roles:
                await member.add_roles(gambler_role)
            await ctx.send(f"{member.mention} is now a Gambler")
        elif ctx.invoked_with == "rg" :
            if gambler_role in member.roles:
                await member.remove_roles(gambler_role)
            await ctx.send(f"{member.mention} is no longer a Gambler")
        else :
            if gambler_role in member.roles:
                await member.remove_roles(gambler_role)
                await ctx.send(f"{member.mention} is no longer a Gambler")
            else :
                await member.add_roles(gambler_role)
                await ctx.send(f"{member.mention} is now a Gambler")
    
async def setup(client):
   await client.add_cog(Extra(client))         
        