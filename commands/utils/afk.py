from email import message
import discord
from discord.ext import commands , tasks
import typing
from botmain import *
from datetime import datetime , time

class AFK(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.afk_users = {}
        self.afk_loop.start()
        self.hl_data = {}
        self.hl_cooldown = {}

    async def cog_load(self): 
        # get the data from afk.json and load it to self.afk_users
        try :
            with open("afk.json" , "r") as f:
                data = json.load(f)
                if 'hl' in data:
                    self.hl_data = {int(k):{int(k2):v2 for k2,v2 in v.items()} for k,v in data['hl'].items()}
                    print(self.hl_data)
                    del data['hl']
                self.afk_users = {int(k):{int(k2):v2 for k2,v2 in v.items()} for k,v in data.items()}
                self.afk_users['hl'] = self.hl_data
                
        except :
            self.afk_users = {}
            with open("afk.json" , "w") as f:
                json.dump(self.afk_users , f)

    async def cog_unload(self):
        self.afk_loop.cancel()
        with open("afk.json" , "w") as f:
            json.dump(self.afk_users , f)
    
    @tasks.loop(minutes = 30)
    # @tasks.loop(seconds=2)
    async def afk_loop(self):
        with open("afk.json" , "w") as f:
            json.dump(self.afk_users , f)

    def check_afk_channel(self , guild_id , channel_id ):
        if guild_id not in self.client.app_command_settings:
            return False
        if 'afksettings' not in self.client.app_command_settings[guild_id]:
            return False

        perms = self.client.app_command_settings[guild_id]['afksettings']
        if perms['channel']['all_channels'] == True and channel_id in perms['channel']['denied']:
            return False
        elif perms['channel']['all_channels'] == False and channel_id not in perms['channel']['allowed']:
            return False 
        return True

    # at_cooldown = commands.CooldownMapping.from_cooldown(1.0,  60, commands.BucketType.user) 
    @commands.Cog.listener()
    async def on_message(self , message):

        if message.guild == None :
            return
        if message.author.bot :
            return   

        # delete the message if it is a forwarded message (in general channel)
        try :
            if message.channel.id == 848874581535358996 and message.reference and message.reference.type == discord.MessageReferenceType.forward :
                await message.delete()
                return
        except :
            pass

        server_hl = self.hl_data.get(message.guild.id , {})
        matched_users = []

        for user_id, hl_text in server_hl.items():
            if hl_text.lower() in message.content.lower() :
                if ((" " + hl_text.lower() + " ") in message.content.lower()) or ( message.content.lower().startswith(hl_text.lower() + " ")) or (message.content.lower().endswith(" " + hl_text.lower()) ) or (message.content.lower() == hl_text.lower()):
                    matched_users.append(user_id) 
        
        if matched_users:
            embed = discord.Embed(title="Highlight" , color=0x2b2c31)
            embed.description = f"{message.content}"
            embed.set_author(name=message.author.name , icon_url=message.author.avatar)
            embed.add_field(name="Source" , value=message.jump_url)
            for user_id in matched_users:
                user = message.guild.get_member(user_id)
                if user and message.author.id != user_id:
                    if message.channel.permissions_for(user).read_messages == True:
                        try :
                            if (user.id not in self.hl_cooldown) or (user.id in self.hl_cooldown and self.hl_cooldown[user.id] < datetime.now().timestamp()) :
                                await user.send(embed=embed)
                                self.hl_cooldown[user.id] = datetime.now().timestamp() + 60
                        except :
                            pass

        if not self.check_afk_channel(message.guild.id , message.channel.id) :
            return
        
        if self.afk_users.get(message.guild.id , {}).get(message.author.id , None) :
            if message.content.lower().startswith("!afk") :
                return
            self.afk_users.get(message.guild.id,{}).pop(message.author.id)
            await message.channel.send(f"Welcome Back {message.author.mention }, I Removed your AFK" , delete_after = 3 )
            try :
                if message.author.nick and message.author.nick.startswith("[AFK]") :
                    new_nick = message.author.nick.replace("[AFK] " , "")
                    await message.author.edit(nick = new_nick)
            except :
                pass
            return

        if message.mentions:
            for user in message.mentions: 
                if self.afk_users.get(message.guild.id , {}).get(user.id , None) :
                    await message.channel.send(f"`{user.display_name.replace('[AFK] ' , '')}` is AFK: {self.afk_users[message.guild.id][user.id]['status']} - <t:{self.afk_users[message.guild.id][user.id]['time']}:R>" , allowed_mentions = discord.AllowedMentions(everyone= False , users= False, roles=False, replied_user=False))
                    return   

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def afk(self,ctx,*,status:str = "AFK"):
        if status.find("https://") != -1 :
            await ctx.send("Set afk without links")
            return    
        
        time = round(datetime.now().timestamp())

        self.afk_users.setdefault(ctx.guild.id,{})
        self.afk_users[ctx.guild.id][ctx.author.id] = {"time":time , "status":status}
        await ctx.send(f"{ctx.author.mention} I set your AFK: {status}" , allowed_mentions = discord.AllowedMentions(everyone= False , users= False, roles=False, replied_user=False))
        try :
            if ctx.author.nick and ctx.author.nick.startswith("[AFK]") :
                return
            new_nick = f"[AFK] {ctx.author.display_name}"
            await ctx.author.edit(nick = new_nick)
        except :
            pass
    
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions( manage_guild = True)
    async def afksettings(self,ctx):
        embed = discord.Embed(title="AFK Settings")
        perms = self.client.app_command_settings.get(ctx.guild.id , {}).get('afksettings' , {})
        if perms['channel']['all_channels'] == True :
            val = "✅ All Channels"
            for channel in perms['channel']['denied']:
                if ctx.guild.get_channel(channel) :
                    val += f"\n🛑 {ctx.guild.get_channel(channel).mention}"
        else :
            val = "❌ All Channels"
            for channel in perms['channel']['allowed']:
                if ctx.guild.get_channel(channel) :
                    val += f"\n🛑 {ctx.guild.get_channel(channel).mention}"
        embed.add_field(name="Channels" , value=val)
        embed.set_footer(text="You can change the settings from server intigrations")
        await ctx.send(embed = embed)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def afklist(self,ctx):
        embed = discord.Embed(title="AFK list")
        i = 1 
        for data in self.afk_users.get(ctx.guild.id,{}) :
            user = ctx.guild.get_member(data)
            try : embed.add_field(name = f"{i}. {user.name} - {user.id}" , value= f"{self.afk_users[ctx.guild.id][data]['status']} : <t:{self.afk_users[ctx.guild.id][data]['time']}:R>")
            except : continue
            i = i+1 
        await ctx.send(embed = embed)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def afkclear(self,ctx , user : discord.Member):
        for data in self.afk_users.get( ctx.guild.id , {} ):
            if data == user.id :
                self.afk_users.get( ctx.guild.id , {} ).pop(data)
                await ctx.send(f"Removed {user.name}'s afk!!!")
                return
            
    @commands.hybrid_command( aliases = ["hl"] )
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def highlight(self , ctx , text : str = None) :
        if text == None :
            if ctx.guild.id in self.hl_data and ctx.author.id in self.hl_data[ctx.guild.id] :
                del self.hl_data[ctx.guild.id][ctx.author.id]
                await ctx.send("Removed your highlight")
            else :
                await ctx.send("You don't have any highlight")
            return
        if len(text) < 3 or len(text) > 50 :
            await ctx.send("Highlight must be between 3 and 50 characters")
            return
        if ctx.guild.id not in self.hl_data:
            self.hl_data[ctx.guild.id] = {}
        self.hl_data[ctx.guild.id][ctx.author.id] = text
        self.afk_users['hl'] = self.hl_data
        await ctx.send(f"Set your highlight to `{text}`")

    @commands.hybrid_command( aliases = ["hlclear" , "clearhl"] )
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def clearhighlight(self , ctx , user : discord.Member = None) :
        if user == None :
            # list all highlights in the server in an embed
            embed = discord.Embed(title="Highlights" , color=0x2b2c31)
            embed.description = ""
            for data in self.hl_data.get(ctx.guild.id , {}).items() :
                user = ctx.guild.get_member(data[0])
                if user == None :
                    # remove the user from the highlights
                    self.hl_data[ctx.guild.id].pop(data[0])
                else :
                    embed.description += f"{user.name} - {data[1]}\n"
            if embed.description == "" :
                await ctx.send("No highlights in the server")
                return
            await ctx.send(embed = embed)
        else :
            if ctx.guild.id in self.hl_data and user.id in self.hl_data[ctx.guild.id] :
                del self.hl_data[ctx.guild.id][user.id]
                self.afk_users['hl'] = self.hl_data
                await ctx.send(f"Removed {user.name}'s highlight")
            else : 
                await ctx.send("This user doesn't have any highlight")
        return

async def setup(client):
   await client.add_cog(AFK(client))