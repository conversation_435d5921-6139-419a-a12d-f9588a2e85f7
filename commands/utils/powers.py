
import discord
from discord.ext import commands
from botmain import *
import typing

class Powers(commands.Cog):

    def __init__(self , client):
        self.client = client
        
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def join(self ,ctx , member : typing.Optional[discord.Member] , vc : typing.Optional[discord.VoiceChannel]):

        if not ctx.author.voice :
            await ctx.send(embed = bembed("You are not in a voice channel"))
            return

        voice_channel = None
        if not member and not vc:
            await ctx.send(embed =bembed("Please provide a member OR a voice channel"))
            return
        if member and member.voice and member.voice.channel:
            voice_channel = member.voice.channel
        elif vc:
            voice_channel = vc
        if not voice_channel:
            await ctx.send(embed = bembed("Member is not in a voice channel"))
            return
        
        if voice_channel.permissions_for(ctx.author).connect == False or voice_channel.permissions_for(ctx.author).view_channel == False:
            await ctx.send(embed = bembed("You don't have permission to join this voice channel"))
            return

        limit = voice_channel.user_limit
        if not limit or limit > len(voice_channel.members):
            await ctx.author.move_to(voice_channel)
            await ctx.send(embed = bembed(f"Moved to {voice_channel.name}"))

        else:
            needed_approval = 0
            approvers = set()
            if limit >= 5 :
                for member in voice_channel.members:
                    if member and member.voice and ( member.voice.self_stream or member.voice.self_video ):
                        approvers.add(member.id)
                        needed_approval += 1
            elif limit == 4 or limit == 3 or limit == 2:
                for member in voice_channel.members:
                    approvers.add(member.id)
                needed_approval = 2
            else :
                needed_approval = 1
                for member in voice_channel.members:
                    approvers.add(member.id)
            text = f"{ctx.author.mention} wants to join {voice_channel.name}.\n\n{ ' '.join([member.mention for member in voice_channel.members if member.id in approvers]) }"
            view = discord.ui.View(timeout=600)
            approve_button = discord.ui.Button(label="Approve")

            async def approve_callback(interaction: discord.Interaction):
                nonlocal needed_approval

                if interaction.user.id in approvers:
                    approvers.remove(interaction.user.id)
                    needed_approval -= 1
                    if needed_approval == 0:
                        view.stop()
                        try :
                            await ctx.author.move_to(voice_channel)
                        except :
                            await ctx.send( ctx.author.mention ,embed =bembed("Failed to move you to the voice channel"))
                            return
                        await ctx.send( ctx.author.mention ,embed = bembed(f"Moved to {voice_channel.name}"))
                    await interaction.response.send_message(f"Approved {ctx.author.mention} to join {voice_channel.name}", ephemeral=True)
                else:
                    await interaction.response.send_message("Already approved or not in the voice channel", ephemeral=True)
            
            approve_button.callback = approve_callback
            view.add_item(approve_button)
            await ctx.send(text, view=view)


        
async def setup(client):
   await client.add_cog(Powers(client))         
        