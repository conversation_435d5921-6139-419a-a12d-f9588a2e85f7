import json
import os
from discord.ext import commands
import discord
from typing import Dict, List
import time

from botmain import check_app_command_permission, bembed

# Load or create the data file
def load_data():
    if os.path.exists('tempvc.json'):
        with open('tempvc.json', 'r') as f:
            return json.load(f)
    return {}

def save_data(data):
    with open('tempvc.json', 'w') as f:
        json.dump(data, f, indent=4)

class TempVC(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.data = load_data()
        self.user_cooldowns = {}  # Store user cooldown timestamps

    def get_next_counter(self, created_vcs):
        if not created_vcs:
            return 1
        
        # Get all used counter numbers
        used_counters = [vc["counter"] for vc in created_vcs]
        used_counters.sort()
        return used_counters[-1] + 1

    def is_user_on_cooldown(self, user_id: int) -> tuple[bool, float]:
        """Check if a user is on cooldown. Returns (is_on_cooldown, remaining_time)"""
        if user_id not in self.user_cooldowns:
            return False, 0
        
        remaining = 30 - (time.time() - self.user_cooldowns[user_id])
        if remaining <= 0:
            del self.user_cooldowns[user_id]  # Clean up expired cooldown
            return False, 0
        return True, remaining

    async def create_vc_list_embed(self, guild_id: str, join_channel_id: str = None) -> discord.Embed:
        """Create an embed showing all temporary VCs in the guild"""
        guild_data = self.data.get(guild_id, {})
        
        if join_channel_id:
            # Show specific join channel VCs
            channel_data = guild_data.get(join_channel_id)
            if not channel_data:
                return bembed("No temporary VCs found for this channel.")
            
            description = f"**Join Channel:** <#{channel_data['vc_id']}>\n"
            description += f"**Template Name:** {channel_data['vc_name']}\n"
            description += f"**Category:** <#{channel_data['category_id']}>\n\n"
            
            if channel_data['created_vcs']:
                description += "**Active VCs:**\n"
                for vc in channel_data['created_vcs']:
                    description += f"• {vc['vc_name']} (<#{vc['vc_id']}>)\n"
            else:
                description += "*No active temporary VCs*"
            
            return bembed(description)
        else:
            # Show all VCs in guild
            if not guild_data:
                return bembed("No temporary VC systems set up in this server.")
            
            description = "**Temporary VC Systems:**\n\n"
            for channel_id, channel_data in guild_data.items():
                description += f"**Join Channel:** <#{channel_data['vc_id']}>\n"
                description += f"**Template Name:** {channel_data['vc_name']}\n"
                description += f"**Category:** <#{channel_data['category_id']}>\n"
                
                if channel_data['created_vcs']:
                    description += "**Active VCs:**\n"
                    for vc in channel_data['created_vcs']:
                        description += f"• {vc['vc_name']} (<#{vc['vc_id']}>)\n"
                else:
                    description += "*No active temporary VCs*\n"
                description += "\n"
            
            return bembed(description)

    async def cleanup_invalid_vcs(self, guild: discord.Guild, guild_data: dict) -> bool:
        """Clean up invalid VCs and return True if any cleanup was done"""
        cleaned = False
        for channel_id, channel_data in list(guild_data.items()):
            # Check if join channel exists
            join_channel = guild.get_channel(channel_data['vc_id'])
            if not join_channel:
                del guild_data[channel_id]
                cleaned = True
                continue

            # Check if category exists
            category = guild.get_channel(channel_data['category_id'])
            if not category:
                del guild_data[channel_id]
                cleaned = True
                continue

            # Check created VCs
            valid_vcs = []
            for vc in channel_data['created_vcs']:
                channel = guild.get_channel(vc['vc_id'])
                if channel:
                    valid_vcs.append(vc)
                else:
                    cleaned = True
            channel_data['created_vcs'] = valid_vcs

        return cleaned

    @commands.hybrid_command(
        name="tempvc",
        description="Set up or remove a temporary voice channel system",
        help="Set up or remove a temporary voice channel system. Usage: !tempvc [join_channel] [vc_name] [category]"
    )
    @commands.has_permissions(manage_channels=True)
    @commands.check(check_app_command_permission)
    async def tempvc(
        self, 
        ctx: commands.Context, 
        join_channel: discord.VoiceChannel = None, 
        vc_name: str = None,
        category: discord.CategoryChannel = None
    ):
        guild_id = str(ctx.guild.id)
        
        # If no arguments provided, show all VCs
        if not join_channel:
            guild_data = self.data.get(guild_id, {})
            if not guild_data:
                await ctx.send(embed=bembed("No temporary VC systems set up in this server."))
                return

            # Clean up invalid VCs
            if await self.cleanup_invalid_vcs(ctx.guild, guild_data):
                save_data(self.data)

            # Show all VCs
            embed = await self.create_vc_list_embed(guild_id)
            await ctx.send(embed=embed)
            return

        channel_id = str(join_channel.id)

        # If the channel is already set up, remove it
        if guild_id in self.data and channel_id in self.data[guild_id]:
            # Get current VCs before deletion
            embed = await self.create_vc_list_embed(guild_id, channel_id)
            
            # Delete all active temporary VCs
            channel_data = self.data[guild_id][channel_id]
            for vc in channel_data['created_vcs']:
                try:
                    channel = ctx.guild.get_channel(vc['vc_id'])
                    if channel:
                        await channel.delete()
                except:
                    pass
            
            # Remove the data
            del self.data[guild_id][channel_id]
            if not self.data[guild_id]:
                del self.data[guild_id]
            save_data(self.data)
            
            embeds = [
                bembed(f"Temporary VC system removed for {join_channel.mention}\n\n**Previous Setup:**"),
                embed
            ]
            await ctx.send(embeds=embeds)
            return

        # If no vc_name or category provided when setting up new system
        if not vc_name or not category:
            await ctx.send("Please provide both vc_name and category to set up a new temporary VC system.")
            return

        # Store the data
        if guild_id not in self.data:
            self.data[guild_id] = {}
            
        self.data[guild_id][channel_id] = {
            "vc_id": join_channel.id,
            "vc_name": vc_name,
            "category_id": category.id,
            "created_vcs": []
        }
        save_data(self.data)
        
        # Create and send embed
        embed = await self.create_vc_list_embed(guild_id, channel_id)
        embeds = [
            bembed(f"Temporary VC system set up! Join {join_channel.mention} to create your own VC.\n\n**Current Setup:**"),
            embed
        ]
        await ctx.send(embeds=embeds)

    @commands.Cog.listener()
    async def on_voice_state_update(self, member: discord.Member, before: discord.VoiceState, after: discord.VoiceState):
        guild_id = str(member.guild.id)
        guild_data = self.data.get(guild_id, {})

        if before.channel:
            # Check if this is a temporary VC (not a join channel)
            for join_channel_id, channel_data in guild_data.items():
                if before.channel.category_id == channel_data["category_id"]:
                    if before.channel.id != int(join_channel_id):
                        # If the channel is empty, delete it
                        if len(before.channel.members) == 0:
                            # Find and remove the VC from created_vcs
                            for vc in channel_data["created_vcs"]:
                                if vc["vc_id"] == before.channel.id:
                                    channel_data["created_vcs"].remove(vc)
                                    await before.channel.delete()
                                    break
                    break
        
        # Handle joining a temp VC channel
        if after.channel:
            channel_data = guild_data.get(str(after.channel.id))
            if channel_data:
                # Check user cooldown
                is_cooldown, remaining = self.is_user_on_cooldown(member.id)
                if is_cooldown:
                    # Send cooldown message in the VC
                    await after.channel.send(member.mention ,
                        embed=bembed(f"⏰ please wait {remaining:.1f} seconds before creating another VC."),
                    )
                    # Move member back to their previous channel or disconnect
                    # await member.move_to(None)
                    return

                # Get the category from stored category_id
                category = member.guild.get_channel(channel_data["category_id"])
                if not category:
                    return

                # Get the next available counter
                vc_name = channel_data["vc_name"]
                counter = self.get_next_counter(channel_data["created_vcs"])
                
                # Copy permissions from the join channel
                overwrites = after.channel.overwrites.copy()
                
                new_vc = await category.create_voice_channel(
                    f"{vc_name}-{counter}",
                    overwrites=overwrites,
                    bitrate=after.channel.bitrate,
                    user_limit=after.channel.user_limit,
                )
                
                # Store the new VC data
                channel_data["created_vcs"].append({
                    "vc_id": new_vc.id,
                    "vc_name": f"{vc_name}-{counter}",
                    "counter": counter
                })
                
                # Set user cooldown
                self.user_cooldowns[member.id] = time.time()
                
                # Move member to new VC
                await member.move_to(new_vc)
                save_data(self.data)

async def setup(bot):
    await bot.add_cog(TempVC(bot)) 