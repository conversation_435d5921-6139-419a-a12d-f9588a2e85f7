import discord
from discord.ext import commands
import time
import asyncio
from typing import Optional, Dict, Any, List
from botmain import client

class StickyMessageManager:
    """
    A comprehensive sticky message utility that provides database-backed
    sticky message functionality for Discord channels.
    """

    def __init__(self, bot: commands.Bot):
        self.bot = bot
        self._cache = {}  # Cache for active sticky messages
        self._refresh_locks = {}  # Locks to prevent concurrent refreshes

    async def create_sticky_message(
        self,
        guild_id: int,
        channel_id: int,
        name: str,
        content: Optional[str] = None,
        embed: Optional[discord.Embed] = None,
        view: Optional[discord.ui.View] = None,
        created_by: int = None
    ) -> Optional[int]:
        """
        Create a new sticky message configuration.

        Args:
            guild_id: Discord guild ID
            channel_id: Discord channel ID
            name: Unique name for this sticky message in the channel
            content: Text content of the message
            embed: Discord embed object
            view: Discord view object with components
            created_by: User ID who created this sticky message

        Returns:
            Sticky message ID if successful, None otherwise
        """
        try:
            # Prepare embed data
            embed_data = None
            if embed:
                embed_data = embed.to_dict()

            # Prepare view data (simplified - store component info)
            view_data = None
            if view:
                view_data = self._serialize_view(view)

            # Insert into database
            sticky_id = await self.bot.db.fetchval(
                """INSERT INTO sticky_messages
                   (guild_id, channel_id, name, content, embed_data, view_data, created_at, created_by)
                   VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                   RETURNING id""",
                guild_id, channel_id, name, content, embed_data, view_data,
                int(time.time()), created_by
            )

            # Send the initial message
            await self._send_sticky_message(sticky_id)

            return sticky_id

        except Exception as e:
            print(f"Error creating sticky message: {e}")
            return None

    async def update_sticky_message(
        self,
        sticky_id: int,
        content: Optional[str] = None,
        embed: Optional[discord.Embed] = None,
        view: Optional[discord.ui.View] = None,
        updated_by: int = None
    ) -> bool:
        """
        Update an existing sticky message.

        Args:
            sticky_id: ID of the sticky message to update
            content: New text content
            embed: New embed object
            view: New view object
            updated_by: User ID who updated this sticky message

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current sticky message data
            sticky_data = await self.bot.db.fetchrow(
                "SELECT * FROM sticky_messages WHERE id = $1", sticky_id
            )

            if not sticky_data:
                return False

            # Prepare update data
            embed_data = embed.to_dict() if embed else sticky_data['embed_data']
            view_data = self._serialize_view(view) if view else sticky_data['view_data']
            content = content if content is not None else sticky_data['content']

            # Update database
            await self.bot.db.execute(
                """UPDATE sticky_messages
                   SET content = $1, embed_data = $2, view_data = $3,
                       updated_at = $4, updated_by = $5
                   WHERE id = $6""",
                content, embed_data, view_data, int(time.time()), updated_by, sticky_id
            )

            # Refresh the message
            await self._refresh_sticky_message(sticky_id)

            return True

        except Exception as e:
            print(f"Error updating sticky message: {e}")
            return False

    async def delete_sticky_message(self, sticky_id: int) -> bool:
        """
        Delete a sticky message configuration and remove the message.

        Args:
            sticky_id: ID of the sticky message to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get sticky message data
            sticky_data = await self.bot.db.fetchrow(
                "SELECT * FROM sticky_messages WHERE id = $1", sticky_id
            )

            if not sticky_data:
                return False

            # Delete the Discord message if it exists
            if sticky_data['message_id']:
                try:
                    channel = self.bot.get_channel(sticky_data['channel_id'])
                    if channel:
                        message = await channel.fetch_message(sticky_data['message_id'])
                        await message.delete()
                except discord.NotFound:
                    pass
                except Exception as e:
                    print(f"Error deleting Discord message: {e}")

            # Remove from database
            await self.bot.db.execute(
                "DELETE FROM sticky_messages WHERE id = $1", sticky_id
            )

            # Remove from cache
            cache_key = f"{sticky_data['guild_id']}_{sticky_data['channel_id']}_{sticky_data['name']}"
            self._cache.pop(cache_key, None)

            return True

        except Exception as e:
            print(f"Error deleting sticky message: {e}")
            return False

    async def get_sticky_messages(self, guild_id: int, channel_id: Optional[int] = None) -> List[Dict]:
        """
        Get sticky messages for a guild or specific channel.

        Args:
            guild_id: Discord guild ID
            channel_id: Optional channel ID to filter by

        Returns:
            List of sticky message configurations
        """
        try:
            if channel_id:
                query = "SELECT * FROM sticky_messages WHERE guild_id = $1 AND channel_id = $2 AND enabled = true"
                params = [guild_id, channel_id]
            else:
                query = "SELECT * FROM sticky_messages WHERE guild_id = $1 AND enabled = true"
                params = [guild_id]

            rows = await self.bot.db.fetch(query, *params)
            return [dict(row) for row in rows]

        except Exception as e:
            print(f"Error getting sticky messages: {e}")
            return []

    async def refresh_sticky_message(self, guild_id: int, channel_id: int, name: str) -> bool:
        """
        Refresh a specific sticky message by name.

        Args:
            guild_id: Discord guild ID
            channel_id: Discord channel ID
            name: Name of the sticky message

        Returns:
            True if successful, False otherwise
        """
        try:
            sticky_data = await self.bot.db.fetchrow(
                "SELECT * FROM sticky_messages WHERE guild_id = $1 AND channel_id = $2 AND name = $3 AND enabled = true",
                guild_id, channel_id, name
            )

            if not sticky_data:
                return False

            return await self._refresh_sticky_message(sticky_data['id'])

        except Exception as e:
            print(f"Error refreshing sticky message: {e}")
            return False

    async def refresh_channel_sticky_messages(self, channel_id: int) -> bool:
        """
        Refresh all sticky messages in a channel.

        Args:
            channel_id: Discord channel ID

        Returns:
            True if successful, False otherwise
        """
        try:
            sticky_messages = await self.bot.db.fetch(
                "SELECT * FROM sticky_messages WHERE channel_id = $1 AND enabled = true",
                channel_id
            )

            for sticky_data in sticky_messages:
                await self._refresh_sticky_message(sticky_data['id'])

            return True

        except Exception as e:
            print(f"Error refreshing channel sticky messages: {e}")
            return False

    async def _send_sticky_message(self, sticky_id: int) -> Optional[discord.Message]:
        """Send a sticky message and update the database with message ID."""
        try:
            sticky_data = await self.bot.db.fetchrow(
                "SELECT * FROM sticky_messages WHERE id = $1", sticky_id
            )

            if not sticky_data:
                return None

            # Check if there's already a message_id set (prevent duplicate sends)
            if sticky_data['message_id']:
                print(f"Debug: Sticky message {sticky_id} already has message_id {sticky_data['message_id']}, skipping send")
                return None

            channel = self.bot.get_channel(sticky_data['channel_id'])
            if not channel:
                return None

            # Prepare message components
            content = sticky_data['content']
            embed = None
            view = None

            if sticky_data['embed_data']:
                embed = discord.Embed.from_dict(sticky_data['embed_data'])

            if sticky_data['view_data']:
                view = self._deserialize_view(sticky_data['view_data'])

            print(f"Debug: Sending sticky message {sticky_id} to channel {channel.id}")

            # Send message
            message = await channel.send(content=content, embed=embed, view=view)

            # Update database with message ID
            await self.bot.db.execute(
                "UPDATE sticky_messages SET message_id = $1 WHERE id = $2",
                message.id, sticky_id
            )

            print(f"Debug: Sticky message {sticky_id} sent successfully with message_id {message.id}")

            return message

        except Exception as e:
            print(f"Error sending sticky message: {e}")
            return None

    async def _refresh_sticky_message(self, sticky_id: int) -> bool:
        """Refresh a sticky message by deleting old and sending new."""
        # Use a lock to prevent concurrent refreshes of the same sticky message
        if sticky_id not in self._refresh_locks:
            self._refresh_locks[sticky_id] = asyncio.Lock()

        async with self._refresh_locks[sticky_id]:
            try:
                print(f"Debug: Starting refresh for sticky message {sticky_id}")

                sticky_data = await self.bot.db.fetchrow(
                    "SELECT * FROM sticky_messages WHERE id = $1", sticky_id
                )

                if not sticky_data:
                    print(f"Debug: Sticky message {sticky_id} not found in database")
                    return False

                # Clear the message_id first to prevent race conditions
                await self.bot.db.execute(
                    "UPDATE sticky_messages SET message_id = NULL WHERE id = $1",
                    sticky_id
                )

                # Delete old message if exists
                if sticky_data['message_id']:
                    try:
                        channel = self.bot.get_channel(sticky_data['channel_id'])
                        if channel:
                            old_message = await channel.fetch_message(sticky_data['message_id'])
                            await old_message.delete()
                            print(f"Debug: Deleted old sticky message {sticky_data['message_id']}")
                    except discord.NotFound:
                        print(f"Debug: Old sticky message {sticky_data['message_id']} not found (already deleted)")
                        pass
                    except Exception as e:
                        print(f"Error deleting old sticky message: {e}")

                # Send new message
                await self._send_sticky_message(sticky_id)

                print(f"Debug: Completed refresh for sticky message {sticky_id}")
                return True

            except Exception as e:
                print(f"Error refreshing sticky message: {e}")
                return False

    def _serialize_view(self, view: discord.ui.View) -> Dict[str, Any]:
        """
        Serialize a Discord view to JSON-compatible format.

        Note: This is a simplified serialization that stores component metadata.
        For complex views with callbacks, you should store a view identifier
        and recreate the view with proper callbacks when deserializing.
        """
        if not view or not view.children:
            return None

        components = []

        for item in view.children:
            if isinstance(item, discord.ui.Button):
                components.append({
                    'type': 'button',
                    'style': item.style.value if item.style else 1,
                    'label': item.label,
                    'emoji': str(item.emoji) if item.emoji else None,
                    'custom_id': item.custom_id,
                    'disabled': item.disabled,
                    'url': getattr(item, 'url', None)
                })
            elif isinstance(item, discord.ui.Select):
                options = []
                for option in item.options:
                    options.append({
                        'label': option.label,
                        'value': option.value,
                        'description': option.description,
                        'emoji': str(option.emoji) if option.emoji else None,
                        'default': option.default
                    })

                components.append({
                    'type': 'select',
                    'custom_id': item.custom_id,
                    'placeholder': item.placeholder,
                    'min_values': item.min_values,
                    'max_values': item.max_values,
                    'disabled': item.disabled,
                    'options': options
                })
            # Add more component types as needed

        return {
            'components': components,
            'timeout': view.timeout,
            'view_class': view.__class__.__name__,  # Store class name for recreation
            'view_module': view.__class__.__module__  # Store module for proper import
        }

    def _deserialize_view(self, view_data: Dict[str, Any]) -> Optional[discord.ui.View]:
        """
        Deserialize JSON data back to a Discord view.

        This method attempts to recreate views with proper callbacks by checking
        for known view classes. For unknown views, it creates basic views without callbacks.
        """
        if not view_data or 'components' not in view_data:
            return None

        # Try to recreate the original view class with callbacks
        view_class_name = view_data.get('view_class')
        view_module = view_data.get('view_module')

        if view_class_name == 'SuggestionButton' and view_module == 'commands.logging.suggestion':
            try:
                from commands.logging.suggestion import SuggestionButton
                return SuggestionButton()
            except ImportError:
                pass

        # Add more known view classes here as needed
        # elif view_class_name == 'AnotherViewClass':
        #     try:
        #         from some.module import AnotherViewClass
        #         return AnotherViewClass()
        #     except ImportError:
        #         pass

        # Fallback: create a basic view without callbacks
        view = discord.ui.View(timeout=view_data.get('timeout'))

        for component_data in view_data['components']:
            try:
                if component_data['type'] == 'button':
                    button = discord.ui.Button(
                        style=discord.ButtonStyle(component_data.get('style', 1)),
                        label=component_data.get('label'),
                        custom_id=component_data.get('custom_id'),
                        disabled=component_data.get('disabled', False),
                        url=component_data.get('url')
                    )
                    view.add_item(button)

                elif component_data['type'] == 'select':
                    options = []
                    for option_data in component_data.get('options', []):
                        option = discord.SelectOption(
                            label=option_data['label'],
                            value=option_data['value'],
                            description=option_data.get('description'),
                            emoji=option_data.get('emoji'),
                            default=option_data.get('default', False)
                        )
                        options.append(option)

                    select = discord.ui.Select(
                        custom_id=component_data.get('custom_id'),
                        placeholder=component_data.get('placeholder'),
                        min_values=component_data.get('min_values', 1),
                        max_values=component_data.get('max_values', 1),
                        disabled=component_data.get('disabled', False),
                        options=options
                    )
                    view.add_item(select)

            except Exception as e:
                print(f"Error deserializing component: {e}")
                continue

        return view if view.children else None

class StickyUtility(commands.Cog):
    """Cog that provides sticky message utility functions."""

    def __init__(self, client):
        self.client = client
        self.sticky_manager = StickyMessageManager(client)

    def get_manager(self) -> StickyMessageManager:
        """Get the sticky message manager instance."""
        return self.sticky_manager

# Global instance for backward compatibility
sticky_manager = None

def get_sticky_manager() -> StickyMessageManager:
    """Get the global sticky message manager instance."""
    global sticky_manager
    if sticky_manager is None:
        sticky_manager = StickyMessageManager(client)
    return sticky_manager

# Convenience functions for easy integration
async def create_sticky(guild_id: int, channel_id: int, name: str, **kwargs) -> Optional[int]:
    """Convenience function to create a sticky message."""
    return await get_sticky_manager().create_sticky_message(guild_id, channel_id, name, **kwargs)

async def update_sticky(sticky_id: int, **kwargs) -> bool:
    """Convenience function to update a sticky message."""
    return await get_sticky_manager().update_sticky_message(sticky_id, **kwargs)

async def delete_sticky(sticky_id: int) -> bool:
    """Convenience function to delete a sticky message."""
    return await get_sticky_manager().delete_sticky_message(sticky_id)

async def refresh_sticky(guild_id: int, channel_id: int, name: str) -> bool:
    """Convenience function to refresh a sticky message."""
    return await get_sticky_manager().refresh_sticky_message(guild_id, channel_id, name)

async def refresh_channel_stickies(channel_id: int) -> bool:
    """Convenience function to refresh all sticky messages in a channel."""
    return await get_sticky_manager().refresh_channel_sticky_messages(channel_id)

async def setup(client):
    """Setup function required by Discord.py for loading this as an extension."""
    await client.add_cog(StickyUtility(client))
