import io
import discord
from discord.ext import commands 
import typing
from botmain import *
from datetime import datetime , timedelta
import re 
import asyncio 
from discord.ui import  View 
import random

time_regex = re.compile(r"(\d{1,5}(?:[.,]?\d{1,5})?)([smhd])")
time_dict = {"h":3600, "s":1, "m":60, "d":86400}

class TimeConverter(commands.Converter):
    async def convert(self, ctx, argument):
        matches = time_regex.findall(argument.lower())
        time = 0
        for v, k in matches:
            try:
                time = time_dict[k]*float(v)
            except KeyError:
                raise commands.BadArgument("{} is an invalid time-key! h/m/s/d are valid!".format(k))
            except ValueError:
                raise commands.BadArgument("{} is not a number!".format(v)) 
        if time == 0 :
            time = 2419200
            argument = "28days"              
        return time , argument

class Modcommands(commands.Cog):

    def __init__(self , client):
        self.client = client
 
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def av(self,ctx,user:typing.Optional[typing.Union[discord.Member, discord.User]] = None ,size:typing.Optional[int]=4096):
        user = user or ctx.author
        embed = discord.Embed(color=0x00000 , description = user.mention, timestamp = datetime.now())
        embed.set_author(name = user, icon_url = user.display_avatar)
        embed.set_image(url = user.display_avatar.with_size(size))
        await ctx.send(embed=embed) 

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions( manage_guild = True)
    async def prefix(self,ctx, prefix : str = None):
        embed = bembed(f"Current Prefix is **{client.data[ctx.guild.id]['prefix'] if client.data[ctx.guild.id]['prefix'] else '!'}**")
        if not prefix :
            await ctx.send(embed=embed) 
        else :
            client.data[ctx.guild.id]['prefix'] = prefix[0]
            await client.db.execute("UPDATE guilds SET prefix = $1 WHERE id = $2" , prefix[0] , ctx.guild.id )
            embed.description = f"Prefix is Updated To **{client.data[ctx.guild.id]['prefix'] if client.data[ctx.guild.id]['prefix'] else '!'}**"
            await ctx.send(embed=embed) 

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def movevc(self , ctx , from_vc : discord.VoiceChannel , to_vc : discord.VoiceChannel ):
        await ctx.defer()
        if not to_vc.permissions_for(ctx.author).connect:
                await ctx.send(f"you are not allowed to drag users in {to_vc.mention}")
                return
        i = 0
        for members in from_vc.members:
            i = i + 1
            await members.move_to(to_vc)
        await ctx.send(f"Done {i} user(s) moved from {from_vc.mention} to {to_vc.mention}")
    
    @commands.hybrid_command(aliases=["wv"])
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def whichvc(self , ctx , user : discord.Member = None):
        '''
        Permission of this command is managed from server integrations
        '''
        await ctx.defer()
        Rauthor = None
        if ctx.message.reference is not None:
            print("here")

            msg = ctx.message.reference.cached_message
            if not msg:
                msg = await ctx.channel.fetch_message(ctx.message.reference.message_id)
            Rauthor = msg.author
        user = user or Rauthor or ctx.author
        data = user.voice
        if data is None :
            if ctx.interaction:
                await ctx.send(f"{user.display_name} is Not in a VC")
            else :
                await ctx.message.add_reaction("🔇")
        else:
            await ctx.reply(data.channel.mention)    

    @commands.hybrid_command(aliases=["ap"])
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def addperms(self,ctx : commands.Context , channel : typing.Optional[discord.abc.GuildChannel] , obj : typing.Union[discord.Member, discord.Role] , permission : typing.Literal['speak' , 'connect' , 'view_channel' , 'send_messages'] = None):
        channel : discord.abc.GuildChannel = channel or ctx.channel
        
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to change channel perms" , delete_after = 3 )
            return
        
        overwrite = channel.overwrites_for(obj)
        if permission is None :
            if isinstance(channel, discord.VoiceChannel) :
                overwrite.connect = True
                overwrite.speak = True
            overwrite.send_messages = True
            overwrite.view_channel = True
        # check if channel is voice channel
        elif permission == 'speak' and isinstance(channel, (discord.VoiceChannel , discord.StageChannel)) :
            overwrite.speak = True
        elif permission == 'connect' and isinstance(channel, (discord.VoiceChannel , discord.StageChannel)) :
            overwrite.connect = True
        elif permission == 'view_channel' :
            overwrite.view_channel = True
        elif permission == 'send_messages' :
            overwrite.send_messages = True
        await channel.set_permissions(obj, overwrite=overwrite)
        overwrite = channel.overwrites_for(obj)
        embed : discord.Embed = bembed(f"Updated Permissions for {obj.mention if isinstance(obj, discord.Role) else obj.mention} in {channel.mention}")
        # ✅ view_channel
        allowed = overwrite.pair()[0]
        allowed_perms = [f"✅ {x[0]}" for x in allowed if x[1] ]
        denied = overwrite.pair()[1]
        denied_perms = [f"❌ {x[0]}" for x in denied if x[1] ]
        embed.add_field(name = "Allowed" , value = '\n'.join(allowed_perms) )
        embed.add_field(name = "Denied" , value = '\n'.join(denied_perms) )
        await ctx.send(embed = embed)
 
    @commands.hybrid_command(aliases=["rmp"])
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def removeperms(self,ctx : commands.Context ,  channel : typing.Optional[discord.abc.GuildChannel] , obj : typing.Union[discord.Member, discord.Role] , permission : typing.Literal['speak' , 'connect' , 'view_channel' , 'send_messages'] = None):
        
        channel : discord.abc.GuildChannel = channel or ctx.channel
        
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to change channel perms" , delete_after = 3 )
            return
        
        overwrite = channel.overwrites_for(obj)
        if permission is None :
            if isinstance(channel, discord.VoiceChannel) :
                overwrite.connect = False
                overwrite.speak = False
            overwrite.send_messages = False
            overwrite.view_channel = False
        # check if channel is voice channel
        elif permission == 'speak' and isinstance(channel, (discord.VoiceChannel , discord.StageChannel)) :
            overwrite.speak = False
        elif permission == 'connect' and isinstance(channel, (discord.VoiceChannel , discord.StageChannel)) :
            overwrite.connect = False
        elif permission == 'view_channel' :
            overwrite.view_channel = False
        elif permission == 'send_messages' :
            overwrite.send_messages = False
        await channel.set_permissions(obj, overwrite=overwrite)
        overwrite = channel.overwrites_for(obj)
        embed : discord.Embed = bembed(f"Updated Permissions for {obj.mention if isinstance(obj, discord.Role) else obj.mention} in {channel.mention}")
        # ✅ view_channel
        allowed = overwrite.pair()[0]
        allowed_perms = [f"✅ {x[0]}" for x in allowed if x[1] ]
        denied = overwrite.pair()[1]
        denied_perms = [f"❌ {x[0]}" for x in denied if x[1] ]
        embed.add_field(name = "Allowed" , value = '\n'.join(allowed_perms) )
        embed.add_field(name = "Denied" , value = '\n'.join(denied_perms) )
        await ctx.send(embed = embed)

    @commands.hybrid_command(aliases=["np"])
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def noneperms(self,ctx : commands.Context , channel : typing.Optional[discord.abc.GuildChannel] , obj : typing.Union[discord.Member, discord.Role]):
        channel : discord.abc.GuildChannel = channel or ctx.channel
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to change channel perms" , delete_after = 3 )
            return
        
        await channel.set_permissions(obj, overwrite=None)
        overwrite = channel.overwrites_for(obj)
        embed : discord.Embed = bembed(f"Empty Permissions for {obj.mention if isinstance(obj, discord.Role) else obj.mention} in {channel.mention}")
        await ctx.send(embed = embed)  
    
    @commands.hybrid_command(aliases=["cp"])
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def checkperms(self,ctx : commands.Context , channel : typing.Optional[discord.abc.GuildChannel] , obj : typing.Optional[typing.Union[discord.Member, discord.Role]]):
        channel : discord.abc.GuildChannel = channel or ctx.channel
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to see channel perms" , delete_after = 3 )
            return
        if obj :
            overwrite = channel.overwrites_for(obj)
            embed : discord.Embed = bembed(f"Updated Permissions for {obj.mention if isinstance(obj, discord.Role) else obj.mention} in {channel.mention}")
            # ✅ view_channel
            allowed = overwrite.pair()[0]
            allowed_perms = [f"✅ {x[0]}" for x in allowed if x[1] ]
            denied = overwrite.pair()[1]
            denied_perms = [f"❌ {x[0]}" for x in denied if x[1] ]
            embed.add_field(name = "Allowed" , value = '\n'.join(allowed_perms) )
            embed.add_field(name = "Denied" , value = '\n'.join(denied_perms) )
            await ctx.send(embed = embed)
        else :
            overwrites = channel.overwrites
            embed = bembed(f"Permissions for {channel.mention}")
            for obj in overwrites:
                overwrite = overwrites[obj]

                allowed_perms = [f"{x[0]}" for x in overwrite.pair()[0] if x[1] ]
                denied_perms = [f"{x[0]}" for x in overwrite.pair()[1] if x[1] ]

                embed.add_field(name = f"{f'Role: {obj}' if isinstance(obj, discord.Role) else f'user: {obj}'}" , value = f"Allowed - `{' ,'.join(allowed_perms) if allowed_perms else 'None'}`\nDenied - `{' ,'.join(denied_perms) if denied_perms else 'None'}`" , inline = False)
            await ctx.send(embed = embed)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def lock(self,ctx : commands.Context , channel : typing.Optional[discord.abc.GuildChannel] = None , role : typing.Optional[discord.Role] = None):
        channel : discord.abc.GuildChannel = channel or ctx.channel
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to change channel perms" , delete_after = 3 )
            return
        overwrites = channel.overwrites
        perms = {}
        for obj in overwrites:
            permOverwrite = overwrites[obj]
            perms[obj.id] = [permOverwrite.pair()[0].value , permOverwrite.pair()[1].value , 'everyone' if obj == ctx.guild.default_role else 'role' if isinstance(obj, discord.Role) else 'user']

        data = {}
        try :
            with open("lock.json" , "r") as f:
                data = json.load(f)
        except :
            with open("lock.json" , "w") as f:
                json.dump( str({}) , f)

        if str(channel.id) in data :
            await ctx.send(f"{channel.mention} is already locked")
            return
        data[str(channel.id)] = perms
        with open("lock.json" , "w") as f:
            json.dump(data , f)

        new_overwrites = {}
        for role in ctx.guild.roles:
            if role.permissions.manage_messages and channel.permissions_for(role).send_messages and channel.permissions_for(role).view_channel and role.mentionable :
                new_overwrites[role] = discord.PermissionOverwrite(send_messages = True)

        for obj in overwrites:
            overwrite = overwrites[obj]
            if role and obj.id == role.id :
                overwrite.send_messages = True
            elif isinstance(obj, discord.Role) and obj.permissions.manage_messages and channel.permissions_for(obj).send_messages and channel.permissions_for(obj).view_channel:
                overwrite.send_messages = True
            elif isinstance(obj, discord.Member):
                continue
            else :
                overwrite.send_messages = False
            new_overwrites[obj] = overwrite
        await channel.edit(overwrites = new_overwrites)
        # await channel.set_permissions(ctx.guild.default_role , send_messages = False)
        embed = bembed(f"Channel {channel.mention} is locked")
        await ctx.send(embed = embed)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def unlock(self,ctx : commands.Context , channel : typing.Optional[discord.abc.GuildChannel] = None):
        channel : discord.abc.GuildChannel = channel or ctx.channel
        if not channel.permissions_for(ctx.author).manage_channels:
            await ctx.send(f"you are not allowed to change channel perms" , delete_after = 3 )
            return
        
        with open("lock.json" , "r") as f:
            data = json.load(f)
        if str(channel.id) not in data :
            await ctx.send(f"{channel.mention} is already unlocked")
            return
        perms = data.get(str(channel.id) , {})
        overwrites = {}
        for obj in perms:
            allowed = perms[obj][0]
            denied = perms[obj][1]
            type = perms[obj][2]
            overwrite = discord.PermissionOverwrite.from_pair(discord.Permissions(allowed) , discord.Permissions(denied))
            if type == 'everyone' :
                overwrites[ctx.guild.default_role] = overwrite
            elif type == 'role' and ctx.guild.get_role(int(obj)) :
                overwrites[ctx.guild.get_role(int(obj))] = overwrite
            elif type == 'user' and ctx.guild.get_member(int(obj)) :
                overwrites[ctx.guild.get_member(int(obj))] = overwrite
        
        await channel.edit(overwrites = overwrites)
        data.pop(str(channel.id))
        with open("lock.json" , "w") as f:
            json.dump(data , f)
        embed = bembed(f"Channel {channel.mention} is unlocked")
        await ctx.send(embed = embed)
        
       
    @commands.hybrid_command( aliases=["rolemap"])
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def dump(self , ctx , role : typing.Optional[commands.RoleConverter] = None , type : typing.Optional[typing.Literal['u' , 'n' , 'i' , 't']] = None , format : typing.Optional[typing.Literal['e']] = None , ping : typing.Optional[bool] = False ):
        await ctx.channel.typing()
        if role is None :
            role = ctx.guild.default_role
        if type == None :
            list = [f"{x} , {x.id} , {x.mention}" for x in role.members]
        if type == 'u' :
            list = [f"{x}" for x in role.members]
        elif type == 'n' :
            list = [f"{x.nick or x}"  for x in role.members]
        elif type == 'i' :
            list = [ f"{x.id}" for x in role.members]
        elif type == 't' :
            list = [ x.mention for x in role.members]
            
        if format is None :
            text = '\n'.join(list)
        elif format == 'e':
            list = [f"{i+1}. {x}" for i , x in enumerate(list)  ]
            text = '\n'.join(list)
        try :     
            await ctx.send(text , allowed_mentions = discord.AllowedMentions(users= ping) )      
        except :
            txt = open("test.txt" , "w")
            new_file = txt.write(text)
            txt.close()
            file = discord.File( "test.txt" , filename= "dump.txt"   )
            await ctx.send(file = file)          
            
   
    @commands.hybrid_command( aliases=["drag", 'mv'])
    @commands.guild_only()
    @commands.has_guild_permissions(move_members = True)
    async def move(self , ctx ,  user : typing.Optional[discord.Member] = None ,  channel : typing.Optional[discord.VoiceChannel] = None, to_user : typing.Optional[discord.Member] = None , to_channel : typing.Optional[discord.VoiceChannel] = None):
        await ctx.defer()
        if channel and to_channel :
            if not to_channel.permissions_for(ctx.author).connect:
                await ctx.send(f"you are not allowed to drag users in {to_channel.mention}")
                return
            i = 0
            for members in channel.members:
                i = i + 1
                await members.move_to(to_channel)
            await ctx.send(f"Done {i} user(s) moved from {channel.mention} to {to_channel.mention}")
            return

        Rauthor = None
        
        if ctx.message.reference is not None:
            msg = ctx.message.reference.cached_message
            Rauthor = msg.author
        user = user or Rauthor 
        if channel is None :
            channel2 = None
            if to_user is not None:
                if to_user.voice is None:
                    await ctx.send(f"{to_user} is not in vc.")
                    return
                channel2 = to_user.voice.channel
            channel = channel2 or ctx.author.voice.channel 
            if channel == None:
                await ctx.send("You didn't provide a vc or else you are not in vc")
                return
        data = user.voice
        if user.id == 752114356682227823 :
                await ctx.send("nope , not allowed")
                return
        if data is None :
            await ctx.send(f"{user} is Not in a VC")
        else:
            if not channel.permissions_for(ctx.author).connect:
                await ctx.send(f"you are not allowed to drag user in {channel.mention}")
                return
            await user.move_to(channel)
            await ctx.reply(f"{user} dragged to {channel.name}")

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def sup(self , ctx , channel : typing.Optional[discord.VoiceChannel] = None , time : TimeConverter = [120 , "2min"]):
        await ctx.defer()
        
        if channel is None :
            channel = ctx.author.voice.channel
            if channel == None:
                await ctx.send("You didn't provide a vc or else you are not in vc")
                return
        
        await channel.set_permissions( ctx.guild.default_role , use_voice_activation = False )
        await ctx.send(f"{channel.mention} is sup. for next {time[1]}")
        await asyncio.sleep(time[0])
        await channel.set_permissions( ctx.guild.default_role , use_voice_activation = None)
        await ctx.reply(f"{channel.mention} is Open Now")


    @commands.hybrid_command(aliases=["nickname" , "name"])
    @commands.guild_only()
    @commands.has_permissions(manage_nicknames = True)
    async def nick(self , ctx ,  user : discord.Member ,  *,nickname : str = None):
        await ctx.defer()
        await user.edit(nick=nickname)
        await ctx.send(f"Nickname updated for user {user} to {nickname}")

    
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def vmute(self , ctx , user : discord.Member):
        await user.edit(mute = True)
        await ctx.send(f"{user} is muted in vc's")

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    async def vunmute(self , ctx , user : discord.Member):
        await user.edit(mute = False)
        await ctx.send(f"{user} is unmuted in vc's now")   

    @commands.hybrid_command(aliases=["out" , "to"])
    @commands.guild_only()
    @commands.has_permissions(moderate_members = True)
    @commands.check(check_app_command_permission)
    async def timeout(self , ctx , user : discord.Member = None):
        await ctx.defer()
        Rauthor = None
        if ctx.message.reference is not None:
            msg = ctx.message.reference.cached_message
            Rauthor = msg.author
        user = user or Rauthor    
        if not user or user.guild_permissions.moderate_members :
            return
        if user.timed_out_until and user.timed_out_until.timestamp() > datetime.now().timestamp() :
            await ctx.message.add_reaction("❌")
            return
        await user.timeout( timedelta(seconds=60) , reason= f"TimeOut with to command , user : {ctx.author}")
        time = datetime.now().timestamp() + 60
        embed = discord.Embed(color=discord.Color.green() , description=f"✅ ***{user} has been Temporarily Muted***")
        embed2 = discord.Embed(color=discord.Color.red() , description=f"✅ ***You have been Muted from {ctx.guild.name} server , Unmute*** <t:{int(time)}:R> \n**Reason** - Temporarily mute")
        #await ctx.send(embed = embed)
        await ctx.message.add_reaction( random.choice(ctx.guild.emojis) )
        try :
            await user.send(embed = embed2)
        except :
            pass        

    @commands.hybrid_command(aliases = ['sm'])
    @commands.guild_only()
    @commands.has_permissions(manage_messages=True)
    async def slowmode(self , ctx, seconds: int = 0 , channel : typing.Optional[discord.TextChannel] = None):
        channel = channel or ctx.channel
        await channel.edit(slowmode_delay=seconds)
        if seconds==0:
            await ctx.reply(f"slowmode removed !")
        else:
            await ctx.reply(f"Set the slowmode in {channel.name} to {seconds} seconds!")
    
    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)
    async def say(self, ctx,*, what : str):
        await ctx.message.delete()
        await ctx.send(f'{what}')

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)    
    async def banword(self , ctx, word : str ):
        rules = await ctx.guild.fetch_automod_rules()
        rule = None 
        for i in reversed(rules) :
            if i.name == client.user.name :
                rule = i
        if rule is None :
          rule = await ctx.guild.create_automod_rule(name =  client.user.name  , event_type = discord.AutoModRuleEventType.message_send , trigger=  discord.AutoModTrigger(  keyword_filter =[word]), actions = [discord.AutoModRuleAction( channel_id=None , duration=None)]  ,enabled = True) 
          await ctx.send(f"`{word}` has been added in automod word list")
          return   
        lis = rule.trigger.keyword_filter
        lis.append(word)
        await rule.edit(trigger = discord.AutoModTrigger(keyword_filter = lis ) )
        await ctx.send( embed = bembed(f"`{word}` has been added in automod word list"))

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions( manage_guild = True)
    async def unbanword(self , ctx,*, word : str ):
        rules = await ctx.guild.fetch_automod_rules()
        rule = None 
        for i in reversed(rules) :
            if i.name ==  client.user.name :
                rule = i
        if rule is None:
            await ctx.send("No word baned by me")
            return         
        lis = rule.trigger.keyword_filter
        try:
            lis.remove(word)
        except :    
            await ctx.send(f"{word} is not in my word-ban list")
            await ctx.author.send(f"word ban list - ```{lis}```")
            return
        await rule.edit(trigger = discord.AutoModTrigger(keyword_filter = lis ) )
        await ctx.send( embed = bembed(f"`{word}` has been removed from automod word list"))

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)    
    async def showword(self , ctx ):
        rules = await ctx.guild.fetch_automod_rules()
        rule = None 
        for i in reversed(rules) :
            if i.name == client.user.name:
                rule = i
        if rule is None :
          rule = await ctx.guild.create_automod_rule(name = client.name , event_type = discord.AutoModRuleEventType.message_send , trigger=  discord.AutoModTrigger(  keyword_filter =[]), actions = [discord.AutoModRuleAction( channel_id=None , duration=None)]  ,enabled = True) 
        lis = rule.trigger.keyword_filter
        await ctx.send( embed = bembed(f"```{' ,'.join(lis)}```"))


    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)
    @commands.check( check_app_command_permission)
    async def synccategory(self , ctx , category : discord.CategoryChannel):
        await ctx.defer()
        overwrites = category.overwrites
        for channel in category.channels:
            try :
                await channel.edit(overwrites = overwrites)
            except :
                await ctx.send(f"Error in syncing {channel.name}")
                pass
        await ctx.send(f"Done syncing {category.name}")

    # @commands.hybrid_command()
    # @commands.guild_only()
    # @cooldown(1, 600, BucketType.user)
    # async def request(self , ctx , channel : discord.VoiceChannel):
    #     await ctx.defer()
    #     data = ctx.author.voice
    #     if data is None or (len(channel.members) < channel.user_limit) :
    #         await ctx.send(f"you are not in vc or vc have space")
    #         ctx.command.reset_cooldown(ctx)
    #         return  
        
    #     view = MyView(timeout=300 , ctx = ctx , channel= channel)    
    #     await ctx.send(f"{ctx.author} wants to join {channel.mention}" , view = view)
    
    # @request.error
    # async def request_error(self,ctx , error):
    #     if isinstance(error, commands.CommandOnCooldown):
    #         sec = int(error.retry_after)
    #         min , sec = divmod(sec, 60)
    #         message = f"⌚ | you cant use this command for next {min}min {sec}seconds."
    #         await ctx.author.send(message)
    #         return 
    #     else :
    #         await ctx.send(error)     

    # @commands.hybrid_command()
    # @commands.guild_only()
    # @commands.has_permissions(manage_emojis_and_stickers = True)   
    # async def addemoji( self , ctx ,   emoji : discord.PartialEmoji , name : typing.Optional[str] ):
    #     temp = await ctx.guild.create_custom_emoji( name = name or emoji.name , image = await emoji.read() )
    #     await ctx.send(f"{temp} is added in guild..")

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(administrator=True)
    async def eventdm(self, ctx, event_id: str, *, message: str):
        """
        Send a DM to all members interested in a specific event
        
        Parameters
        -----------
        event_id: The ID of the event to target
        message: The message to send to all interested members
        """
        await ctx.defer()
        
        try:
            # Get the event by ID
            event = await ctx.guild.fetch_scheduled_event(int(event_id))
            if not event:
                return await ctx.send(embed=bembed("Event not found."))
                
            # Get all users interested in the event with pagination
            users = await event.users()
            status_message = await ctx.send(embed=bembed("Fetching interested users..."))
                
            if not users:
                return await ctx.send(embed=bembed("No users are interested in this event."))
            
            await status_message.edit(embed=bembed(f"Found {len(users)} interested users. Sending DMs..."))
            
            # Send DMs to all users
            success_count = 0
            failed_users = []  # Track failed users and reasons
            
            embed = discord.Embed(
                title=f"Message from {ctx.guild.name} Event",
                description=message,
                color=0x2b2c31,
                timestamp=datetime.now()
            )
            embed.set_author(name=event.name)
            embed.set_footer(text=f"Sent by {ctx.author}")
            
            progress_interval = max(1, len(users) // 10)  # Update progress every 10% or each message if < 10 users
            
            for i, user in enumerate(users):
                try:
                    await user.send(embed=embed)
                    success_count += 1
                except discord.Forbidden:
                    failed_users.append((user, "Cannot send messages to this user (DMs closed or blocked)"))
                except discord.HTTPException as e:
                    failed_users.append((user, f"HTTP Error: {e.status} - {e.text}"))
                except Exception as e:
                    failed_users.append((user, f"Unexpected error: {str(e)}"))
                
                # Add delay between messages to avoid rate limits (5 per 5 seconds is safe)
                if (i + 1) % 5 == 0:
                    await asyncio.sleep(5)
                else:
                    await asyncio.sleep(1)  # Small delay between each message
                
                # Update progress periodically
                if (i + 1) % progress_interval == 0 or i + 1 == len(users):
                    await status_message.edit(embed=bembed(
                        f"Progress: {i+1}/{len(users)} ({((i+1)/len(users))*100:.1f}%)\n"
                        f"✅ Success: {success_count}\n"
                        f"❌ Failed: {len(failed_users)}"
                    ))
            
            # Send summary
            result = f"✅ Message sent to {success_count} users"
            if failed_users:
                result += f"\n❌ Failed to send to {len(failed_users)} users"
                
            await ctx.send(embed=bembed(result))
            
            # Send detailed failure report if there are any failures
            if failed_users:
                # Create a formatted list of failures
                failure_details = ""
                for i, (user, reason) in enumerate(failed_users, 1):
                    if i <= 20:  # Show first 20 failures in the embed
                        failure_details += f"{i}. {user.name} ({user.id}): {reason}\n"
                
                failure_embed = discord.Embed(
                    title=f"Failed DM Details ({len(failed_users)} users)",
                    description=failure_details[:4000] if failure_details else "No details available",
                    color=discord.Color.red()
                )
                
                if len(failed_users) > 20:
                    failure_embed.set_footer(text=f"Showing 20/{len(failed_users)} failures. Full list in the text file.")
                
                await ctx.send(embed=failure_embed)
                
                # If there are many failures, create a text file with all details
                if len(failed_users) > 20:
                    full_report = f"Failed DM Report for Event: {event.name} ({event.id})\n"
                    full_report += f"Total Users: {len(users)}, Successful: {success_count}, Failed: {len(failed_users)}\n\n"
                    full_report += "DETAILED FAILURE LIST:\n"
                    
                    for i, (user, reason) in enumerate(failed_users, 1):
                        full_report += f"{i}. User: {user.name} (ID: {user.id})\n   Reason: {reason}\n\n"
                    
                    # Create and send the file
                    file = discord.File(
                        io.BytesIO(full_report.encode('utf-8')),
                        filename=f"event_dm_failures_{event.id}.txt"
                    )
                    await ctx.send("Full failure report:", file=file)
            
        except Exception as e:
            await ctx.send(embed=bembed(f"Error: {str(e)}"))

class MyView(View):

    def __init__(self ,  timeout , ctx , channel):
        super().__init__(timeout = timeout)
        self.ctx = ctx 
        self.channel = channel
        self.reactions = []

    @discord.ui.button(label = "0"  , emoji = "➕" , style=discord.ButtonStyle.green)  
    async def button1(self ,interaction ,  button ):

        if interaction.user in self.reactions :
            await interaction.response.send_message(f"you already voted" , ephemeral = True)
        elif interaction.user in self.channel.members :
            self.reactions.append(interaction.user)
            button.label = str(int(button.label) + 1 )
            await interaction.response.edit_message(view=self)
            await interaction.followup.send("vote added" , ephemeral = True)
        else :
            await interaction.response.send_message(f"you are not in {self.channel.mention}" , ephemeral = True)    

        if (set(self.channel.members)).issubset(set(self.reactions)) and (len(self.channel.members) != 0):
            if self.ctx.author.voice == None :
                await interaction.followup.send(f"{self.ctx.author.mention} drag approved but your are not in vc") 
                return
            await self.ctx.author.move_to(self.channel)
            await interaction.message.delete()

async def setup(client):
   await client.add_cog(Modcommands(client))         
