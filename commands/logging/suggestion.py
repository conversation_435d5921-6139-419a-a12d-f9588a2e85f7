import discord
from discord.ext import commands
from discord import app_commands
from botmain import *
from typing import Literal
import time
from commands.utils.sticky import get_sticky_manager

# Static variables
SUGGESTION_CHANNEL_ID = 1319058341187555378
# SUGGESTION_CHANNEL_ID = 1343671852274286652  # Global suggestion channel ID
STICKY_NAME = "suggestion_button"

class SuggestionModal(discord.ui.Modal, title="Submit a Suggestion"):
    def __init__(self):
        super().__init__()
        self.suggestion = discord.ui.TextInput(
            label="Your Suggestion",
            placeholder="Enter your suggestion here...",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=1000
        )
        self.add_item(self.suggestion)

    async def on_submit(self, interaction: discord.Interaction):
        suggestion_channel = interaction.client.get_channel(SUGGESTION_CHANNEL_ID)

        if not suggestion_channel:
            await interaction.response.send_message("Error: Suggestion channel not found.", ephemeral=True)
            return

        # Store suggestion in database and get suggestion number
        try:
            suggestion_id = await interaction.client.db.fetchval(
                """INSERT INTO suggestions (message_id, user_id, guild_id, content, created_at)
                   VALUES ($1, $2, $3, $4, $5) RETURNING suggestion_id""",
                0,  # Temporary message_id, will update after message is sent
                interaction.user.id,
                interaction.guild.id,
                self.suggestion.value,
                int(time.time())
            )
        except Exception as e:
            print(f"Error storing suggestion: {e}")
            await interaction.response.send_message("Error: Failed to store suggestion.", ephemeral=True)
            return

        embed = discord.Embed(
            title=f"Suggestion #{suggestion_id}",
            description=self.suggestion.value,
            color=0x2b2d31,
        )
        embed.set_author(name=interaction.user.display_name, icon_url=interaction.user.display_avatar.url)
        embed.set_footer(text=f"Suggestion #{suggestion_id}")

        msg = await suggestion_channel.send(embed=embed)

        # Update the message_id in the database
        try:
            await interaction.client.db.execute(
                "UPDATE suggestions SET message_id = $1 WHERE suggestion_id = $2",
                msg.id, suggestion_id
            )
        except Exception as e:
            print(f"Error updating message_id: {e}")

        await interaction.response.send_message(f"Your suggestion has been submitted as **Suggestion #{suggestion_id}**. Thank you!", ephemeral=True)

        await msg.add_reaction("⬆️")
        await msg.add_reaction("⬇️")

        # Refresh the sticky message using the new utility
        sticky_manager = get_sticky_manager()
        await sticky_manager.refresh_sticky_message(
            interaction.guild.id,
            SUGGESTION_CHANNEL_ID,
            STICKY_NAME
        )

class SuggestionButton(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Suggest", style=discord.ButtonStyle.secondary, custom_id="suggest_button")
    async def suggest_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        await interaction.response.send_modal(SuggestionModal())

class Suggestion(commands.Cog):
    def __init__(self, client):
        self.client = client

    async def cog_load(self):
        self.client.add_view(SuggestionButton())

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.has_permissions(manage_guild=True)
    async def setupsuggestion(self, ctx):
        """Creates a sticky message with a suggestion button in the suggestion channel"""
        channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)

        if not channel:
            await ctx.send("Error: Suggestion channel not found.")
            return

        # Create embed for the sticky message
        embed = discord.Embed(
            title="Suggestions",
            description="Click the button below to submit a suggestion!",
            color=0x2b2d31
        )

        # Create view with the suggestion button
        view = SuggestionButton()

        # Use the sticky message utility to create/update the sticky message
        sticky_manager = get_sticky_manager()

        # Check if sticky message already exists
        existing_stickies = await sticky_manager.get_sticky_messages(ctx.guild.id, channel.id)
        existing_sticky = next((s for s in existing_stickies if s['name'] == STICKY_NAME), None)

        if existing_sticky:
            # Update existing sticky message
            success = await sticky_manager.update_sticky_message(
                existing_sticky['id'],
                embed=embed,
                view=view,
                updated_by=ctx.author.id
            )
            if success:
                await ctx.send(f"✅ Suggestion sticky message updated in {channel.mention}!")
            else:
                await ctx.send("❌ Failed to update suggestion sticky message.")
        else:
            # Create new sticky message
            sticky_id = await sticky_manager.create_sticky_message(
                guild_id=ctx.guild.id,
                channel_id=channel.id,
                name=STICKY_NAME,
                embed=embed,
                view=view,
                created_by=ctx.author.id
            )
            if sticky_id:
                await ctx.send(f"✅ Suggestion sticky message created in {channel.mention}!")
            else:
                await ctx.send("❌ Failed to create suggestion sticky message.")

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def reply(self, ctx, identifier: str, status: Literal["Approve", "Denied", "Considered", "Empty"] = "Empty", *, reason: str = None):
        """Reply to a suggestion with a status and reason

        Parameters
        -----------
        identifier: The suggestion number (e.g., "5") or message ID to reply to
        status: The status to set (Approve, Denied, Considered, or Empty to reset)
        reason: The reason for the decision (optional)
        """
        channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)

        if not channel:
            await ctx.send("Error: Suggestion channel not found.")
            return

        message_id = None
        suggestion_id = None

        # Check if identifier is a suggestion number or message ID
        try:
            # Try to parse as suggestion number first (shorter numbers)
            if len(identifier) <= 10 and identifier.isdigit():
                suggestion_id = int(identifier)
                # Get message ID from database
                result = await self.client.db.fetchrow(
                    "SELECT message_id, suggestion_id FROM suggestions WHERE suggestion_id = $1 AND guild_id = $2",
                    suggestion_id, ctx.guild.id
                )
                if result:
                    message_id = result['message_id']
                else:
                    await ctx.send(f"Error: Suggestion #{suggestion_id} not found.", ephemeral=True)
                    return
            else:
                # Treat as message ID
                message_id = int(identifier)
                # Get suggestion ID from database
                result = await self.client.db.fetchrow(
                    "SELECT suggestion_id FROM suggestions WHERE message_id = $1",
                    message_id
                )
                if result:
                    suggestion_id = result['suggestion_id']
        except ValueError:
            await ctx.send("Error: Invalid identifier. Use either a suggestion number or message ID.", ephemeral=True)
            return
        except Exception as e:
            print(f"Database error: {e}")
            await ctx.send("Error: Database error occurred.", ephemeral=True)
            return

        try:
            message = await channel.fetch_message(message_id)

            if not message.embeds or len(message.embeds) == 0:
                await ctx.send("Error: This message is not a suggestion.")
                return

            original_embed = message.embeds[0]

            embed = discord.Embed(
                description=original_embed.description,
            )

            if original_embed.author:
                embed.set_author(
                    name=original_embed.author.name,
                    icon_url=original_embed.author.icon_url
                )

            # Preserve the suggestion number in footer
            if suggestion_id:
                embed.set_footer(text=f"Suggestion #{suggestion_id}")
            elif original_embed.footer:
                embed.set_footer(
                    text=original_embed.footer.text,
                    icon_url=original_embed.footer.icon_url
                )

            if status == "Approve":
                embed.title = f"Suggestion #{suggestion_id} | Approved"
                embed.color = discord.Color.green()
                db_status = "approved"
            elif status == "Denied":
                embed.title = f"Suggestion #{suggestion_id} | Denied"
                embed.color = discord.Color.red()
                db_status = "denied"
            elif status == "Considered":
                embed.title = f"Suggestion #{suggestion_id} | Considered"
                embed.color = discord.Color.gold()
                db_status = "considered"
            else:  # Empty - reset to default
                embed.title = f"Suggestion #{suggestion_id}"
                embed.color = 0x2b2d31
                db_status = "pending"

                await message.edit(embed=embed)

                # Update database status and get user info for DM
                user_id = None
                if suggestion_id:
                    try:
                        result = await self.client.db.fetchrow(
                            "UPDATE suggestions SET status = $1, updated_at = $2 WHERE suggestion_id = $3 RETURNING user_id",
                            db_status, int(time.time()), suggestion_id
                        )
                        if result:
                            user_id = result['user_id']
                    except Exception as e:
                        print(f"Error updating suggestion status: {e}")

                # Send DM to the user about status reset
                if user_id:
                    try:
                        user = await self.client.fetch_user(user_id)
                        if user:
                            # Create DM embed for reset
                            dm_embed = discord.Embed(
                                title=f"Your Suggestion #{suggestion_id} status has been reset",
                                description=original_embed.description,
                                color=0x2b2d31
                            )

                            dm_embed.add_field(
                                name="Status Update",
                                value="Your suggestion status has been reset to pending for review.",
                                inline=False
                            )

                            # Add link to original message
                            if message_id:
                                channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)
                                if channel:
                                    message_link = f"https://discord.com/channels/{ctx.guild.id}/{channel.id}/{message_id}"
                                    dm_embed.add_field(
                                        name="View Suggestion",
                                        value=f"[Jump to Message]({message_link})",
                                        inline=False
                                    )

                            dm_embed.set_footer(text=f"From {ctx.guild.name}")

                            await user.send(embed=dm_embed)
                            print(f"DM sent to user {user.display_name} about suggestion #{suggestion_id} reset")

                    except discord.Forbidden:
                        print(f"Could not DM user {user_id} - DMs disabled")
                        await ctx.send("Failed to send DM to user. DMs disabled.", ephemeral=True)
                    except Exception as e:
                        print(f"Error sending DM to user {user_id}: {e}")
                        await ctx.send("Error sending DM to user.", ephemeral=True)

                await ctx.send("Suggestion reset to its original state.", ephemeral=True)
                return

            # Copy existing fields except response fields
            for field in original_embed.fields:
                if field.name.startswith("Response from"):
                    continue  # Skip existing response fields
                embed.add_field(name=field.name, value=field.value, inline=field.inline)

            # Add new response field
            if reason:
                embed.add_field(
                    name=f"Response from {ctx.author.display_name}",
                    value=reason,
                    inline=False
                )

            await message.edit(embed=embed)

            # Update database status and get user info for DM
            user_id = None
            if suggestion_id:
                try:
                    result = await self.client.db.fetchrow(
                        "UPDATE suggestions SET status = $1, updated_at = $2 WHERE suggestion_id = $3 RETURNING user_id",
                        db_status, int(time.time()), suggestion_id
                    )
                    if result:
                        user_id = result['user_id']
                except Exception as e:
                    print(f"Error updating suggestion status: {e}")

            # Send DM to the user who submitted the suggestion
            if user_id:
                try:
                    user = await self.client.fetch_user(user_id)
                    if user:
                        # Create DM embed
                        dm_embed = discord.Embed(
                            title=f"Your Suggestion #{suggestion_id} has been {status}",
                            description=original_embed.description,
                            color=embed.color
                        )

                        if reason:
                            dm_embed.add_field(
                                name=f"Response from {ctx.author.display_name}",
                                value=reason,
                                inline=False
                            )

                        # Add link to original message
                        if message_id:
                            channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)
                            if channel:
                                message_link = f"https://discord.com/channels/{ctx.guild.id}/{channel.id}/{message_id}"
                                dm_embed.add_field(
                                    name="View Suggestion",
                                    value=f"[Jump to Message]({message_link})",
                                    inline=False
                                )

                        dm_embed.set_footer(text=f"From {ctx.guild.name}")

                        await user.send(embed=dm_embed)
                        print(f"DM sent to user {user.display_name} about suggestion #{suggestion_id}")

                except discord.Forbidden:
                    print(f"Could not DM user {user_id} - DMs disabled")
                    await ctx.send("Failed to send DM to user. DMs disabled.", ephemeral=True)
                except Exception as e:
                    print(f"Error sending DM to user {user_id}: {e}")
                    await ctx.send("Error sending DM to user.", ephemeral=True)

            suggestion_ref = f"Suggestion #{suggestion_id}" if suggestion_id else f"Message {message_id}"
            await ctx.send(f"{suggestion_ref} updated with status: {status}\n\nReason: {reason}", ephemeral=True)

        except discord.NotFound:
            await ctx.send("Error: Message not found. Make sure you provided the correct identifier.", delete_after=10, ephemeral=True)
        except Exception as e:
            await ctx.send(f"An error occurred: {str(e)}", delete_after=10, ephemeral=True)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def suggestion(self, ctx, suggestion_number: int):
        """Look up a suggestion by its number

        Parameters
        -----------
        suggestion_number: The suggestion number to look up
        """
        try:
            result = await self.client.db.fetchrow(
                """SELECT s.*, u.username FROM suggestions s
                   LEFT JOIN users u ON s.user_id = u.id
                   WHERE s.suggestion_id = $1 AND s.guild_id = $2""",
                suggestion_number, ctx.guild.id
            )

            if not result:
                await ctx.send(f"Suggestion #{suggestion_number} not found.", ephemeral=True)
                return

            # Create embed with suggestion info
            embed = discord.Embed(
                title=f"Suggestion #{result['suggestion_id']}",
                description=result['content'],
                color=0x2b2d31
            )

            # Try to get user info
            try:
                user = await self.client.fetch_user(result['user_id'])
                embed.set_author(name=user.display_name, icon_url=user.display_avatar.url)
            except:
                embed.set_author(name=f"User ID: {result['user_id']}")

            # Add status info
            status_colors = {
                'pending': 0x2b2d31,
                'approved': discord.Color.green(),
                'denied': discord.Color.red(),
                'considered': discord.Color.gold()
            }

            status = result['status'] or 'pending'
            embed.color = status_colors.get(status, 0x2b2d31)
            embed.add_field(name="Status", value=status.title(), inline=True)

            # Add timestamps
            created_time = f"<t:{result['created_at']}:F>"
            embed.add_field(name="Created", value=created_time, inline=True)

            if result['updated_at']:
                updated_time = f"<t:{result['updated_at']}:F>"
                embed.add_field(name="Last Updated", value=updated_time, inline=True)

            # Add message link if available
            if result['message_id']:
                channel = self.client.get_channel(SUGGESTION_CHANNEL_ID)
                if channel:
                    message_link = f"https://discord.com/channels/{ctx.guild.id}/{channel.id}/{result['message_id']}"
                    embed.add_field(name="Message Link", value=f"[Jump to Message]({message_link})", inline=False)

            await ctx.send(embed=embed, ephemeral=True)

        except Exception as e:
            print(f"Error looking up suggestion: {e}")
            await ctx.send("An error occurred while looking up the suggestion.", ephemeral=True)

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def suggestions(self, ctx, status: Literal["all", "pending", "approved", "denied", "considered"] = "all", limit: int = 10):
        """List recent suggestions

        Parameters
        -----------
        status: Filter by status (default: all)
        limit: Number of suggestions to show (max 20, default: 10)
        """
        if limit > 20:
            limit = 20

        try:
            # Build query based on status filter
            if status == "all":
                query = """SELECT suggestion_id, content, status, user_id, created_at
                          FROM suggestions WHERE guild_id = $1
                          ORDER BY suggestion_id DESC LIMIT $2"""
                params = [ctx.guild.id, limit]
            else:
                query = """SELECT suggestion_id, content, status, user_id, created_at
                          FROM suggestions WHERE guild_id = $1 AND status = $2
                          ORDER BY suggestion_id DESC LIMIT $3"""
                params = [ctx.guild.id, status, limit]

            results = await self.client.db.fetch(query, *params)

            if not results:
                await ctx.send(f"No suggestions found with status: {status}", ephemeral=True)
                return

            # Create embed
            embed = discord.Embed(
                title=f"Recent Suggestions ({status.title()})",
                color=0x2b2d31
            )

            status_emojis = {
                'pending': '⏳',
                'approved': '✅',
                'denied': '❌',
                'considered': '🤔'
            }

            suggestion_list = []
            for result in results:
                status_emoji = status_emojis.get(result['status'] or 'pending', '⏳')
                content_preview = result['content'][:50] + "..." if len(result['content']) > 50 else result['content']

                # Try to get username
                try:
                    user = await self.client.fetch_user(result['user_id'])
                    username = user.display_name
                except:
                    username = f"User {result['user_id']}"

                suggestion_list.append(
                    f"{status_emoji} **#{result['suggestion_id']}** by {username}\n"
                    f"└ {content_preview}"
                )

            embed.description = "\n\n".join(suggestion_list)
            embed.set_footer(text=f"Showing {len(results)} of {len(results)} suggestions")

            await ctx.send(embed=embed, ephemeral=True)

        except Exception as e:
            print(f"Error listing suggestions: {e}")
            await ctx.send("An error occurred while listing suggestions.", ephemeral=True)

async def setup(client):
    await client.add_cog(Suggestion(client))