from discord.ext import commands
from botmain import *
import time
from typing import Optional, Union
import discord

class VCLogs(commands.Cog):
    def __init__(self, client):
        self.client = client
        self.guild_ids = [848588208165748737, 1325171820592238592]
        self.event_types = {
            'join': '🟢',
            'leave': '🔴',
            'move': '🟡'
        }

    @commands.Cog.listener()
    async def on_voice_state_update(self, member, before, after):
        if member.bot or member.guild.id not in self.guild_ids:
            return
        if before.channel == after.channel:
            return

        event_type = 'move'
        if not before.channel:
            event_type = 'join'
        elif not after.channel:
            event_type = 'leave'

        event_time = int(time.time())
        from_channel_id = before.channel.id if before.channel else None
        from_channel_name = before.channel.name if before.channel else 'Deleted Channel'
        to_channel_id = after.channel.id if after.channel else None
        to_channel_name = after.channel.name if after.channel else 'Deleted Channel'

        query = """
            INSERT INTO voice_logs
            (guild_id, user_id, event_type, from_channel_id, from_channel_name,
             to_channel_id, to_channel_name, event_time)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """
        await self.client.db.execute(
            query, member.guild.id, member.id, event_type,
            from_channel_id, from_channel_name,
            to_channel_id, to_channel_name, event_time
        )

    async def get_user_logs(self, guild_id, user_id, page):
        offset = (page - 1) * 25

        # Use window function to get count and data in single query
        query = """
            SELECT *, COUNT(*) OVER() as total_count
            FROM voice_logs
            WHERE guild_id = $1 AND user_id = $2
            ORDER BY event_time DESC
            LIMIT 25 OFFSET $3
        """

        records = await self.client.db.fetch(query, guild_id, user_id, offset)
        if not records:
            return [], 1

        total_count = records[0]['total_count']
        total_pages = (total_count + 24) // 25  # Ceiling division
        return records, total_pages

    async def get_channel_logs(self, guild_id, channel_id, page):
        offset = (page - 1) * 25

        # Use window function to get count and data in single query
        query = """
            SELECT *, COUNT(*) OVER() as total_count
            FROM voice_logs
            WHERE guild_id = $1 AND (from_channel_id = $2 OR to_channel_id = $2)
            ORDER BY event_time DESC
            LIMIT 25 OFFSET $3
        """

        records = await self.client.db.fetch(query, guild_id, channel_id, offset)
        if not records:
            return [], 1

        total_count = records[0]['total_count']
        total_pages = (total_count + 24) // 25  # Ceiling division
        return records, total_pages

    async def get_combined_logs(self, guild_id, user_id, channel_id, page):
        offset = (page - 1) * 25

        # Use window function to get count and data in single query
        query = """
            SELECT *, COUNT(*) OVER() as total_count
            FROM voice_logs
            WHERE guild_id = $1
            AND user_id = $2
            AND (from_channel_id = $3 OR to_channel_id = $3)
            ORDER BY event_time DESC
            LIMIT 25 OFFSET $4
        """

        records = await self.client.db.fetch(query, guild_id, user_id, channel_id, offset)
        if not records:
            return [], 1

        total_count = records[0]['total_count']
        total_pages = (total_count + 24) // 25  # Ceiling division
        return records, total_pages

    class PaginationView(discord.ui.View):
        def __init__(self, cog, guild_id, user, channel, page, total_pages):
            super().__init__(timeout=180)  # Increase timeout to 3 minutes
            self.cog = cog
            self.guild_id = guild_id
            self.user = user
            self.channel = channel
            self.page = page
            self.total_pages = total_pages
            self.message = None
            
            # Set initial button states
            self.update_buttons()

        async def on_timeout(self):
            # Disable buttons when view times out
            for item in self.children:
                item.disabled = True
            
            if self.message:
                try:
                    await self.message.edit(view=self)
                except:
                    pass

        def update_buttons(self):
            self.prev_button.disabled = self.page == 1
            self.next_button.disabled = self.page == self.total_pages

        async def create_embed(self, records):
            
            guild = self.cog.client.get_guild(self.guild_id)
            entries = []
            for record in records:
                event_type = record['event_type']
                event_time = record['event_time']
                from_name = record['from_channel_name']
                to_name = record['to_channel_name']
                from_channel_id = record['from_channel_id']
                to_channel_id = record['to_channel_id']
                user_id = record['user_id']

                member = guild.get_member(user_id) if guild else None
                user_mention = member.mention if member else "Unknown User"
                emoji = self.cog.event_types.get(event_type, '⚪')

                from_ch = f"<#{from_channel_id}> ({from_name})" if from_channel_id else None
                to_ch = f"<#{to_channel_id}> ({to_name})" if to_channel_id else None

                entry = f"`{emoji}` <t:{event_time}:T> "

                if self.user and self.channel:
                    if event_type == 'join':
                        entry += f"{user_mention} joined {to_ch}"
                    elif event_type == 'leave':
                        entry += f"{user_mention} left {from_ch}"
                    else:
                        entry += f"{user_mention} moved {from_ch} → {to_ch}"
                elif self.user:
                    if event_type == 'join':
                        entry += f"Joined {to_ch}"
                    elif event_type == 'leave':
                        entry += f"Left {from_ch}"
                    else:
                        entry += f"Moved {from_ch} → {to_ch}"
                else:
                    if event_type == 'join':
                        entry += f"{user_mention} joined {to_ch}"
                    elif event_type == 'leave':
                        entry += f"{user_mention} left {from_ch}"
                    else:
                        entry += f"{user_mention} moved {from_ch} → {to_ch}"

                entries.append(entry)

            title = self.generate_title()
            embed = discord.Embed(color=discord.Color.blue(), description="\n".join(entries))
            embed.title = title
            embed.set_footer(text=f"Page {self.page}/{self.total_pages}")
            return embed

        def generate_title(self):
            if self.user and self.channel:
                return f"{self.user.display_name}'s logs in #{self.channel.name}"
            elif self.user:
                return f"{self.user.display_name}'s voice logs"
            else:
                return f"Logs for #{self.channel.name}"

        @discord.ui.button(label="Previous", style=discord.ButtonStyle.grey)
        async def prev_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Acknowledge the interaction immediately
            await interaction.response.defer()

            self.page -= 1
            await self.handle_page_change(interaction)

        @discord.ui.button(label="Next", style=discord.ButtonStyle.grey)
        async def next_button(self, interaction: discord.Interaction, button: discord.ui.Button):
            # Acknowledge the interaction immediately
            await interaction.response.defer()

            self.page += 1
            await self.handle_page_change(interaction)

        async def handle_page_change(self, interaction):
            try:
                # Fetch the data
                if self.user and self.channel:
                    records, total_pages = await self.cog.get_combined_logs(
                        self.guild_id, self.user.id, self.channel.id, self.page
                    )
                elif self.user:
                    records, total_pages = await self.cog.get_user_logs(
                        self.guild_id, self.user.id, self.page
                    )
                else:
                    records, total_pages = await self.cog.get_channel_logs(
                        self.guild_id, self.channel.id, self.page
                    )
                
                self.total_pages = total_pages
                self.update_buttons()
                embed = await self.create_embed(records)
                
                # Edit the original message directly since we've already deferred
                await self.message.edit(embed=embed, view=self)
            except Exception as e:
                print(f"Error in pagination: {str(e)}")

    @commands.hybrid_command(aliases=["vlogs", "vlog", "vcs"])
    @commands.guild_only()
    @commands.check(check_app_command_permission)
    async def vclogs(self, ctx,
                    user: Optional[Union[discord.Member, discord.User]] = None,
                    channel: Optional[discord.VoiceChannel] = None,
                    page: int = 1):
        await ctx.defer()

        if not user and not channel:
            return await ctx.send("Specify at least a user or channel.")

        guild_id = ctx.guild.id
        try:
            if user and channel:
                records, total_pages = await self.get_combined_logs(guild_id, user.id, channel.id, page)
            elif user:
                records, total_pages = await self.get_user_logs(guild_id, user.id, page)
            else:
                records, total_pages = await self.get_channel_logs(guild_id, channel.id, page)
        except Exception as e:
            return await ctx.send(f"Error fetching logs: {str(e)}")

        if not records:
            return await ctx.send("No logs found matching your criteria.")

        view = self.PaginationView(
            cog=self,
            guild_id=guild_id,
            user=user,
            channel=channel,
            page=page,
            total_pages=total_pages
        )

        embed = await view.create_embed(records)
        view.message = await ctx.send(embed=embed, view=view)

async def setup(client):
    await client.add_cog(VCLogs(client))
