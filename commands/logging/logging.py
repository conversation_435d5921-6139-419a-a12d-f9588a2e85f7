import datetime
import discord
from discord.ext import commands  
from botmain import *   
import traceback

class ErrorLogging(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.client.error_logging_ch_id = 1322202250788995156 #aui-bot-logs
        # self.client.error_logging_ch_id = 1325171820592238597 

        # self.messages = { }
        # self.bot_messages_channel = 1325171820592238597
        
    # @commands.Cog.listener()
    # async def on_message(self, message):
    #     if message.author.bot:
    #         return
        
    #     if message.channel.id == self.bot_messages_channel and 
        
    @commands.Cog.listener()
    async def on_command_error(self, ctx, error):
        ignored_errors = (commands.CommandNotFound, commands.CommandOnCooldown, commands.CheckFailure, commands.MissingRequiredArgument , commands.DisabledCommand,commands.BadArgument, ValueError, commands.errors.BadLiteralArgument)
        if isinstance(error, ignored_errors):
            return
        else:
            traceback_msg = ''.join(traceback.format_exception(type(error), error, error.__traceback__))
            invite = ctx.guild.vanity_url or (await ctx.guild.invites())[0] if ctx.guild.me.guild_permissions.manage_guild and await ctx.guild.invites() else (await ctx.guild.channels[0].create_invite() if ctx.guild.me.guild_permissions.create_instant_invite else '')
            content = f"**Author:** [{ctx.author.name}](<https://discordapp.com/users/{ctx.author.id}>)\n**Server:** {ctx.guild.name} - [Invite]({invite or '`Not available`'})\n**Message:** `{ctx.message.content}` - {ctx.message.jump_url}"
            try:
                max_size = 1700
                chunks = [traceback_msg[i:i+max_size] for i in range(0, len(traceback_msg), max_size)]
                i = 1
                for chunk in chunks:
                    channel = self.client.get_channel(self.client.error_logging_ch_id)
                    if channel is None:
                        channel = self.client.get_channel(self.client.error_logging_ch_id)
                    await channel.send(f"{content}\n**Message no.:** {i}/{len(chunks)}\n```{chunk}```")
                    i += 1
            except Exception as e:
                pass

    @commands.Cog.listener()
    async def on_command_completion(self, ctx):
        embed = discord.Embed(title= ctx.command.name, description= f"{ctx.author.mention} `{ctx.author.id }`\n{ctx.channel.mention} `{ctx.channel.id}`\n<t:{int(datetime.datetime.now().timestamp())}:F>", color= 0x2b2c31)
        if not ctx.interaction:
            embed.add_field(name= "Message", value= f"[{ctx.message.content}]({ctx.message.jump_url}) " , inline= False)
            if ctx.message.reference is not None:
                msg = ctx.message.reference.cached_message
                embed.add_field(name= "Replying to", value= f"[{msg.content}]({msg.jump_url})" , inline= False)
        else:
            embed.add_field(name= "Type", value= "Slash Command", inline= False)
        
        data = ''
        for i in ctx.kwargs:
            data += f"{i} : {ctx.kwargs[i]}\n"
        if data != '':
            embed.add_field(name= "Arguments", value= data, inline= False)
        
        embed.set_footer(text= f"id : {ctx.author.id}" , icon_url= ctx.author.avatar)

        channel = self.client.get_channel(self.client.error_logging_ch_id)
        if channel :
            await channel.send(embed= embed)


async def setup(client):
  await client.add_cog(ErrorLogging(client))