import discord
from discord.ext import commands , tasks
from botmain import *
from datetime import datetime 
import asyncio
from discord.ui import View , Button , Select
import chat_exporter
import io
import google.generativeai as genai

defult_prefix = "!"

def guild_prefix(guild_id : int):
    return client.data[guild_id]['prefix'] or defult_prefix

defult_mail = {
    "status" : False ,
    "name" : "{no} - {name}" ,
    "dm_message" : "We've opened a Mail, and the support team will get back to you as soon as they can." ,
    "message" : "Suppost team there is a new mail" ,
    "support_roles" : [] ,
    "additional_roles" : [] ,
    "open_category" : None ,
    "close_category" : None ,
    "log" : None ,
    "options" : { }
    }

defult_option = { "description" : None ,
                  "message" : None ,
                  "dm_message" : None ,
                  "support_roles" : [] ,
                  "emoji" : None ,
                }

def get_emoji(str):
    if str == None :
        return None
    if type(str) == int or str.isnumeric() :
        return client.get_emoji(int(str))
    # try :
        # return discord.PartialEmoji.from_str(str)
    # except :
    return None

class PannelView(View):
    
    def __init__(self , lable : str = "Open Mail" , emoji : discord.PartialEmoji = None ):
        super().__init__( timeout=None )
        button = discord.ui.Button(label= lable or "Open Mail" , style= discord.ButtonStyle.gray , emoji = emoji , custom_id='mail:open' , row=0)
        async def callback(interaction):
            mail = await client.db.fetchrow('SELECT * FROM mails WHERE user_id = $1', interaction.user.id)
            if mail and interaction.guild.get_channel(mail['channel_id']) :
                await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Already Have A Mail") , ephemeral=True , view = View().add_item( Button( label= interaction.guild.get_channel(mail['channel_id']).name if not mail['dm'] else "DM" , url = interaction.guild.get_channel(mail['channel_id']).jump_url )) )
                return
            elif mail and not interaction.guild.get_channel(mail['channel_id']) :
                await client.db.execute('DELETE FROM mails WHERE user_id = $1 AND guild_id = $2', interaction.user.id , interaction.guild.id)
            
            await interaction.response.defer( thinking = True , ephemeral=True )
            if len(client.data[interaction.guild.id]['mail']['options']) > 0 :
                select = discord.ui.Select(placeholder="Select Option", min_values=1, max_values=1, )
                for option in client.data[interaction.guild.id]['mail']['options'] :
                    select.add_option(label=option, value=option , emoji= get_emoji(client.data[interaction.guild.id]['mail']['options'][option]['emoji']) )
                async def select_callback(interaction1) :
                    await interaction1.response.defer( thinking = True , ephemeral=True )
                    channel = await Mail.openMail(interaction.user , interaction.guild , interaction1.data["values"][0] , False)
                    await interaction1.followup.send(embed = discord.Embed( color = discord.Color.green() ,  description= client.data[interaction.guild.id]['mail']['options'][option]['dm_message'] or client.data[interaction.guild.id]['mail']['dm_message']) , view =  View().add_item(Button(url= channel.jump_url , label = channel.name )) , ephemeral=True)
                    interaction1.view.stop()
                select.callback = select_callback
                await interaction.followup.send( embed = discord.Embed( description= f"Do You Want To Open A Mail ? , Select Your Mail Reason" , color= 0x2b2c31 ) , view=discord.ui.View().add_item(select) , ephemeral = True)
            else :
                channel = await Mail.openMail(interaction.user , interaction.guild , dm=False)
                await interaction.followup.send(embed = discord.Embed( color = discord.Color.green() ,  description= f"Mail has been opened in {channel.mention}") , view =  View().add_item(Button( label = channel.name ,  url= channel.jump_url  )) , ephemeral=True)
        button.callback = callback
        self.add_item(button)

class ControlView(View):

    def __init__(self):
        super().__init__( timeout=None )

    @discord.ui.button( label= "summary" , style= discord.ButtonStyle.green , custom_id='mail:summary' , row=None)
    async def summary_ticket(self , interaction , button):
        button.disabled = True
        await interaction.response.edit_message(view=self)
        await interaction.channel.typing()
        genai.configure(api_key="AIzaSyD-klFe9lXe7Lq5lO27_FoTnGX4I563edc")
        model = genai.GenerativeModel('gemini-2.0-flash-exp') 
        messages = [message async for message in interaction.channel.history(limit=500)]
        convo = ""
        for msg in reversed(messages):
            
            if not msg.content or msg.content == "" :
                continue
            convo += f"{msg.author.name} : {msg.content}\n"
        
        convo += f"\nSummarize the above conversation for me"
  
        try :
            response = model.generate_content(convo , generation_config=genai.types.GenerationConfig(max_output_tokens=2000) ,safety_settings=None )#{'HARASSMENT':'block_none'})
        except :
            await interaction.followup.send("Some Error Occured" , ephemeral=True)
            return
        await interaction.followup.send(str(response.text))
 
        
    @discord.ui.button( label= "Delete" , style= discord.ButtonStyle.danger  , custom_id='mail:delete' , row=None)
    async def delete_ticket(self , interaction , button): 
        channel = interaction.channel
        
        await interaction.response.send_message( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {interaction.user.mention}") )
        await asyncio.sleep(10)
        await channel.delete()
        return

class DmManageView(View):
    
    def __init__(self):
        super().__init__( timeout=None )
    
    @discord.ui.button( label= "Claim" , style= discord.ButtonStyle.blurple , custom_id='mail:claim' , row=None)
    async def claim_ticket(self , interaction , button):
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and not interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Claim This Mail") , ephemeral=True)
            return
        
        if interaction.channel.id not in Mail.claims :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"Mail Has Been already Closed") , ephemeral=True)
        if interaction.user.id in Mail.claims[interaction.channel.id] :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Have Already Claimed This Mail\n- {guild_prefix(interaction.guild.id)}unclaim - To unclaim Mail") , ephemeral=True)
            return
        
        Mail.claims[interaction.channel.id].append(interaction.user.id)
        await client.db.execute('UPDATE mails SET claims = $1 WHERE channel_id = $2', Mail.claims[interaction.channel.id], interaction.channel.id)
        button.disabled = True
        await interaction.response.edit_message(view=self)
        await interaction.followup.send(embed = discord.Embed( color = discord.Color.green() ,  description= f"Modmail has been claimed by {interaction.user.mention}"))
        await asyncio.sleep(4)
        button.disabled = False
        await interaction.message.edit(view=self)
    

    @discord.ui.button( label= "User Info" , custom_id='mail:dm_userinfo' , row=None)
    async def userinfo_ticket(self , interaction , button):
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and not interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Get User Info") , ephemeral=True)
            return

        mail = await client.db.fetchrow('SELECT * FROM mails WHERE channel_id = $1', interaction.channel.id)
        if mail is None :
            return
        
        embed = discord.Embed( color = discord.Color.blurple() ,  title= f"User Info" , colour= 0x2b2c31 )
        user  = interaction.guild.get_member(mail['user_id'])
        if user and user.voice :
            data = f"{user.voice.channel.mention} {user.voice.channel.name}\n"
            for member in user.voice.channel.members :
                data += f"- `{member.id}` {member.mention}\n"
            embed.add_field(name="Voice Channel" , value=data , inline=False)
        if user.timed_out_until :
            embed.add_field(name="Timed Out" , value=f"Unmute <t:{int(user.timed_out_until.timestamp())}:R>" , inline=False)
        if user.premium_since :
            embed.add_field(name="Boosting Since" , value=f"<t:{int(user.premium_since.timestamp())}:R>" , inline=False)
        if user.pending :
            embed.add_field(name="Pending (member verification)" , value="True" , inline=False)
        if user.nick :
            embed.add_field(name="Nick" , value=user.nick , inline=False)
        if user.is_on_mobile() :
            embed.add_field(name="Mobile" , value="True" , inline=False)
        if len(embed.fields) == 0 and (not embed.description) :
            embed.description = "No Data Found"
        await interaction.response.send_message(embed=embed)


    @discord.ui.button( label= "Modlogs"  , custom_id='mail:dm_modlogs' , row=None)
    async def modlogs_ticket(self , interaction , button):
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and not interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Get Modlogs here") , ephemeral=True)
            return

        mail = await client.db.fetchrow('SELECT * FROM mails WHERE channel_id = $1', interaction.channel.id)
        if mail is None :
            return
        member_id = Mail.mails[interaction.channel.id]
        member = interaction.guild.get_member(member_id)
        await interaction.response.defer(thinking=True , ephemeral=False)
        data = await client.db.fetch('SELECT * FROM modlogs WHERE user_id = $1 AND guild_id = $2 AND action != $3 ORDER BY "case_id" DESC LIMIT 10'  , member.id , interaction.guild.id , "Manual")
        dis = " "

        def action_icon(action):
            if action == "Warn" :
                return "⚠️"
            elif action == "Mute" :
                return "<:timeout:1152941855466590338>"
            elif action == "Unmute" :
                return "<:timeout_remove:1152941974320578581>"
            elif action == 'Kick' :
                return '<:kick:1152941711568416921>'
            elif action == 'Ban' :
                return '<:ban:1152942226645712936>'
            elif action == 'UnBan' :
                return "<:unban:1152942111193301062>"
            else :
                return ""

        for case in data:
                mod = interaction.guild.get_member(case['mod_id'])
                if mod is None or mod == interaction.guild.me :
                    mod = case['mod']
                else :
                    mod = mod.mention
                dis = dis + f"**Case {case['case_id']}**\n**Action** - {action_icon(case['action'])} {case['action']}\n**Mod** - {mod}\n**Reason** - {discord.utils.remove_markdown(case['reason']) if case['reason'] else 'No Reason Provided' } , <t:{case['time']}:R>\n"
                if case['duration'] is not None :
                    dis = dis + f"**Duration** - {case['duration']}\n\n"
                else:
                    dis = dis + "\n" 
        embed = discord.Embed(color= discord.Color.blue() , title= f"{member}'s Modlogs" , description=dis).set_footer(text=f"Id : {member.id}")
        if dis == " " :
            embed.description = "🚫 No modlogs found"
        elif len(data) == 10:
            embed.set_footer(text=f"Id : {member.id} | ⚠️ This only shows the last 10 modlogs")
        await interaction.followup.send( interaction.user.mention ,embed = embed)

    # @discord.ui.button( label= "summary" , custom_id='mail:dm_summary' , row=None)
    # async def summary_ticket(self , interaction , button):
    #     button.disabled = True
    #     await interaction.response.edit_message(view=self)
    #     await interaction.channel.typing()
    #     genai.configure(api_key="AIzaSyD-klFe9lXe7Lq5lO27_FoTnGX4I563edc")
    #     model = genai.GenerativeModel('gemini-2.0-flash-exp') 
    #     messages = [message async for message in interaction.channel.history(limit=500)]
    #     convo = ""
    #     for msg in reversed(messages):
            
    #         if not msg.content or msg.content == "" or msg.author == interaction.guild.me :
    #             continue
    #         convo += f"{msg.author.name} : {msg.content}\n"
        
    #     convo += f"\nSummarize the above conversation for me"
  
    #     try :
    #         response = model.generate_content(convo , generation_config=genai.types.GenerationConfig(max_output_tokens=2000) ,safety_settings=None )#{'HARASSMENT':'block_none'})
    #     except :
    #         await interaction.followup.send("Some Error Occured" , ephemeral=True)
    #         return
    #     await interaction.followup.send(str(response.text))


    @discord.ui.button( label= "Close" , style= discord.ButtonStyle.danger , custom_id='mail:dm_close' , row=None)
    async def close_ticket(self , interaction , button):
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and not interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Close This Mail") , ephemeral=True)
            return
        if interaction.channel.id not in Mail.mails :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"Mail Has Been already Closed") , ephemeral=True)
            return
        
        value = Mail.mails[interaction.channel.id]
        del Mail.mails[interaction.channel.id]
        del Mail.claims[interaction.channel.id]
        await client.db.execute('DELETE FROM mails WHERE channel_id = $1', interaction.channel.id)
        link = None
        
        await interaction.response.defer( thinking= True )
        
        if client.data[interaction.guild.id]["mail"]['log'] and interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['log']) :
            transcript = await chat_exporter.export(channel=  interaction.channel, limit = 100 , tz_info="Asia/Kolkata")
            transcript_file = discord.File(io.BytesIO(transcript.encode()), filename=f"{interaction.channel.name}.html")
            msg = await  interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['log']).send( file=transcript_file)
            link = await chat_exporter.link(msg)
            if interaction.guild.get_member(value) :
                embed = discord.Embed( title= interaction.channel.name , description= f"- Created By : {interaction.guild.get_member(value).mention} `{interaction.guild.get_member(value).id}`\n- Closed By : {interaction.user.mention}" , color= embed_color)
            else :
                embed = discord.Embed( title= interaction.channel.name , description= f"- Created By : {value}\n- Closed By : {interaction.user.mention}" , color= embed_color)
            await msg.reply(embed=embed , view = View().add_item(Button(label="Transcript" , url=link)))
        
        if client.data[interaction.guild.id]["mail"]['close_category'] and interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['close_category']) :
            
            await interaction.channel.edit(category= interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['close_category']))
            if link :
                await interaction.followup.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {interaction.user.mention}") , view=ControlView().add_item(Button(label="Transcript" , url=link)) )              
            else :
                await interaction.followup.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {interaction.user.mention}") , view=ControlView())
        else :
            if link :
                await interaction.followup.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {interaction.user.mention}") , view= View().add_item(Button(label="Transcript" , url=link)) )
            else :
                await interaction.followup.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {interaction.user.mention}") )
            await asyncio.sleep(10)
            await interaction.channel.delete()
        if interaction.guild.get_member(value) :
            await interaction.guild.get_member(value).send(embed=discord.Embed( color = discord.Color.red() ,  description= f"Your Mail Has Been Closed"))


class ManageView(View):
    
    def __init__(self):
        super().__init__(timeout=None)
    
    @discord.ui.button( label= "Close" , style= discord.ButtonStyle.danger , custom_id='mail:close' , row=None)
    async def close_ticket(self , interaction , button):
        mail = await client.db.fetchrow('SELECT * FROM mails WHERE channel_id = $1', interaction.channel.id)
        if mail is None :
            return
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Close This Mail") , ephemeral=True)
            return
        
        await interaction.response.defer(thinking = True)
        
        value = mail['user_id']
        await client.db.execute('DELETE FROM mails WHERE channel_id = $1', interaction.channel.id)
        link = None
        if client.data[interaction.guild.id]["mail"]['log'] and interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['log']) :
            transcript = await chat_exporter.export(channel=  interaction.channel, limit = 100 , tz_info="Asia/Kolkata")
            transcript_file = discord.File(io.BytesIO(transcript.encode()), filename=f"{interaction.channel.name}.html")
            msg = await  interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['log']).send( file=transcript_file)
            link = await chat_exporter.link(msg)
            if interaction.guild.get_member(value) :
                embed = discord.Embed( title= interaction.channel.name , description= f"- Created By : {interaction.guild.get_member(value).mention} `{interaction.guild.get_member(value).id}`\n- Closed By : {interaction.user.mention}" , color= embed_color , )
            else :
                embed = discord.Embed( title= interaction.channel.name , description= f"- Created By : {value}\n- Closed By : {interaction.user.mention}" , color= embed_color , )
            await msg.reply(embed=embed , view = View().add_item(Button(label="Transcript" , url=link)))
        
        if client.data[interaction.guild.id]["mail"]['close_category'] and interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['close_category']) :
            
            overwrites = interaction.channel.overwrites
            for overwrite in dict(overwrites) :
                if type(overwrite) == discord.Member :
                    overwrites.pop(overwrite)
            
            await interaction.channel.edit(category= interaction.guild.get_channel(client.data[interaction.guild.id]["mail"]['close_category']) , overwrites = overwrites)
            if link :
                await interaction.followup.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {interaction.user.mention}") , view=ControlView().add_item(Button(label="Transcript" , url=link)) )              
            else :
                await interaction.followup.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {interaction.user.mention}") , view=ControlView())
        else :
            if link :
                await interaction.followup.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {interaction.user.mention}") , view= View().add_item(Button(label="Transcript" , url=link)) )
            else :
                await interaction.followup.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {interaction.user.mention}") )
            await asyncio.sleep(10)
            await interaction.channel.delete()

    @discord.ui.button( label= "User Info" , style= discord.ButtonStyle.blurple , custom_id='mail:userinfo' , row=None)
    async def userinfo_ticket(self , interaction , button):
        role_ids = list(client.data[interaction.guild.id]['mail']['support_roles'])
        if interaction.channel.topic is not None :
            role_ids.extend(client.data[interaction.guild.id]['mail']['options'].get(interaction.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in interaction.user.roles ])) and not interaction.user.guild_permissions.manage_guild :
            await interaction.response.send_message(embed = discord.Embed( color = discord.Color.red() ,  description= f"You Don't Have Permission To Get User Info") , ephemeral=True)
            return

        mail = await client.db.fetchrow('SELECT * FROM mails WHERE channel_id = $1', interaction.channel.id)
        if mail is None :
            return
        
        embed = discord.Embed( color = discord.Color.blurple() ,  title= f"User Info" , colour= 0x2b2c31)
        user  = interaction.guild.get_member(mail['user_id'])
        if user and user.voice :
            data = f"{user.voice.channel.mention} {user.voice.channel.name}\n"
            for member in user.voice.channel.members :
                data += f"- `{member.id}` {member.mention}\n"
            embed.add_field(name="Voice Channel" , value=data , inline=False)
        if user.timed_out_until :
            embed.add_field(name="Timed Out" , value=f"Unmute <t:{int(user.timed_out_until.timestamp())}:R>" , inline=False)
        if user.premium_since :
            embed.add_field(name="Boosting Since" , value=f"<t:{int(user.premium_since.timestamp())}:R>" , inline=False)
        if user.pending :
            embed.add_field(name="Pending (member verification)" , value="True" , inline=False)
        if user.nick :
            embed.add_field(name="Nick" , value=user.nick , inline=False)
        if user.is_on_mobile() :
            embed.add_field(name="Mobile" , value="True" , inline=False)
        await interaction.response.send_message(embed=embed , ephemeral=True)


class Mail(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.client.add_view(ControlView())
        self.client.add_view(ManageView())
        self.client.add_view(DmManageView())
        self.client.add_view(PannelView())
        self.load_mails.start()
        self.pings_updates.start()

        Mail.mails = { } # channel_id : user_id
        Mail.claims = { } # channel_id : [ user_id ]
        Mail.pings = { } # channel_id : {time : (in seconds) , stage : 0} 
    
    async def cog_unload(self) -> None:
        self.load_mails.cancel()
        self.pings_updates.cancel()

    @tasks.loop(seconds=10 , count=1) 
    async def load_mails(self):
        mails = await client.db.fetch("SELECT * FROM mails")
        Mail.mails = { mail['channel_id'] : mail['user_id'] for mail in mails if mail['dm'] }
        Mail.claims = { mail['channel_id'] : mail['claims'] for mail in mails if mail['dm'] }
    
    @tasks.loop(seconds=10 , reconnect=True)
    async def pings_updates(self):
        for channel_id in Mail.pings :
            try :
                Mail.pings[channel_id]['time'] += 10
                if Mail.pings[channel_id]['time'] >= 120 and Mail.pings[channel_id]['stage'] == 0 :
                    # if claimed ping the users and amke stage 1
                    Mail.pings[channel_id]['stage'] = 1
                    users = Mail.claims[channel_id]
                    channel = client.get_channel(channel_id)
                    if channel :
                        if len(users) > 0 :
                            await channel.send(f"BOT PING : {', '.join([ ('<@' + str(user) + '>') for user in users])}")
                elif Mail.pings[channel_id]['time'] >= 600 and Mail.pings[channel_id]['stage'] == 1 :
                    #  make a ping to all support_roles if option + 
                    Mail.pings[channel_id]['stage'] = 2
                    channel = client.get_channel(channel_id)
                    if channel :
                        await channel.send(f"BOT PING : @here")
            except :
                pass

    @load_mails.before_loop
    async def before_load_mails(self):
        await client.wait_until_ready()
    

    @commands.hybrid_command(aliases= ["mailsetup", "setup-mail" , "mail-setup" , "mail" , "mod-mail" , "modmail" ])
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)
    async def setupmail(self, ctx):
        
        def get_data() :
            data = { **defult_mail , **(self.client.data[ctx.guild.id]["mail"] if self.client.data[ctx.guild.id]["mail"] else {} )}
            for option in data["options"] :
                data["options"][option] = { **defult_option , **data["options"][option] }
            return data
                    
        def setupmailEmbed():
            
            data = get_data()
            
            embed = discord.Embed(title="Mail Settings" , color= 0x2b2c31 ,  ) 
            embed.description = f"DM Mail Status : **{'ON' if data['status'] else 'OFF'}**\n\n"
            embed.description += f"- **Mail Name :** **{data['name']}**\n"
            embed.description += f"- **Defult Message :** {data['message']}\n"
            embed.description += f"- **Defult Dm Message :** {data['dm_message']}\n"
            embed.description += f"- **Support Roles :** { ' '.join([ ctx.guild.get_role(role_id).mention for role_id in data['support_roles'] if ctx.guild.get_role(role_id) ]) if len(data['support_roles']) >0 else 'Admin Only' }\n"
            embed.description += f"- **Additional Roles :** {' '.join([ ctx.guild.get_role(role_id).mention for role_id in data['additional_roles'] if ctx.guild.get_role(role_id) ]) if len(data['additional_roles'])>0 else 'None' }\n"
            embed.description += f"- **Open Category :** {ctx.guild.get_channel(data['open_category']).mention if data['open_category'] and ctx.guild.get_channel(data['open_category']) else 'None'}\n"
            embed.description += f"- **Close Category :** {ctx.guild.get_channel(data['close_category']).mention if data['close_category'] and ctx.guild.get_channel(data['close_category']) else 'None'}\n"
            embed.description += f"- **Log Channel :** {ctx.guild.get_channel(data['log']).mention if data['log'] and ctx.guild.get_channel(data['log']) else 'None'}\n"
            
            for i , option in  enumerate(data["options"]):
                value = f"- **Description :** {data['options'][option]['description']}\n"
                value += f"- **Message :** {data['options'][option]['message'] or 'Defult Message'}\n"
                value += f"- **DM Message :** {data['options'][option]['dm_message'] or 'Defult Dm Message'}\n"
                value += f"- **Support Roles :** {' '.join([ ctx.guild.get_role(role_id).mention for role_id in data['options'][option]['support_roles'] if ctx.guild.get_role(role_id) ]) if len(data['options'][option]['support_roles'])>0 else 'None' }\n"
                
                name = f"{i+1}. {(str(get_emoji(data['options'][option]['emoji'])) if (get_emoji(data['options'][option]['emoji'])) else '')} {option}"
                embed.add_field(name=name , value=value , inline=False)
                
            return embed
        
        async def select_callback(interaction) :
            if interaction.user != ctx.author:
                await interaction.response.send_message("Not Your Interaction" , ephemeral = True)
                return
            if interaction.data["values"][0] == "status" :
                data = get_data()
                data["status"] = not data["status"]
                await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                client.data[ctx.guild.id]["mail"] = data
                await interaction.response.edit_message(embed=setupmailEmbed() , view=view)
            elif interaction.data["values"][0] == "name" :
                modal = discord.ui.Modal( title="Change Name Template" , timeout=180)
                modal.add_item(discord.ui.TextInput(label="Name Template", placeholder="{no} - {name}/{category}", default =get_data()["name"]))
                modal.on_error = lambda *args, **kwargs: None
                modal.on_timeout = lambda *args, **kwargs: None
                async def on_submit(interaction):
                    data = get_data()
                    data["name"] = modal.children[0].value
                    client.data[ctx.guild.id]["mail"] = data
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    await interaction.response.edit_message(embed=setupmailEmbed() , view=view)
                modal.on_submit = on_submit
                await interaction.response.send_modal(modal)
            elif interaction.data["values"][0] == "messages" :
                modal = discord.ui.Modal( title="Change Messages" , timeout=180)
                modal.add_item(discord.ui.TextInput(label="Defult Message", placeholder="Defult Message", default =get_data()["message"]))
                modal.add_item(discord.ui.TextInput(label="Defult Dm Message", placeholder="Defult Dm Message", default =get_data()["dm_message"] , style= discord.TextStyle.paragraph))
                modal.on_error = lambda *args, **kwargs: None
                modal.on_timeout = lambda *args, **kwargs: None
                async def on_submit(interaction):
                    data = get_data()
                    data["message"] = modal.children[0].value
                    data["dm_message"] = modal.children[1].value
                    client.data[ctx.guild.id]["mail"] = data
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    await interaction.response.edit_message(embed=setupmailEmbed() , view=view)
                modal.on_submit = on_submit
                await interaction.response.send_modal(modal)
            elif interaction.data["values"][0] == "support_roles" :
                select = discord.ui.RoleSelect(placeholder=" Support Role", min_values=0 , max_values=25 , default_values= [ ctx.guild.get_role(role_id) for role_id in get_data()["support_roles"] if ctx.guild.get_role(role_id) ])
                async def update_s_roles(interaction1):
                    data = get_data()
                    data['support_roles'] = [ item.id for item in select.values ]
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    client.data[ctx.guild.id]["mail"] = data
                    await interaction1.response.defer()
                    await interaction.message.edit(embed=setupmailEmbed() , view=view)
                select.callback = update_s_roles
                await interaction.response.send_message("Select Support Roles" , view=discord.ui.View().add_item(select) , ephemeral = True)
            elif interaction.data["values"][0] == "additional_roles" :
                select = discord.ui.RoleSelect(placeholder=" Additional Role", min_values=0 , max_values=25 , default_values= [ ctx.guild.get_role(role_id) for role_id in get_data()["additional_roles"] if ctx.guild.get_role(role_id) ])
                async def update_a_roles(interaction1):
                    data = get_data()
                    data['additional_roles'] = [ item.id for item in select.values ]
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    client.data[ctx.guild.id]["mail"] = data
                    await interaction1.response.defer()
                    await interaction.message.edit(embed=setupmailEmbed() , view=view)
                select.callback = update_a_roles
                await interaction.response.send_message("Select Additional Roles" , view=discord.ui.View().add_item(select) , ephemeral = True)
            elif interaction.data["values"][0] == "open_category" :
                select = discord.ui.ChannelSelect(channel_types=[discord.ChannelType.category], placeholder="Mail Open Category", min_values=0, max_values=1 , default_values= [ ctx.guild.get_channel(get_data()["open_category"])] if get_data()["open_category"] else [] )
                async def update_o_category(interaction1):
                    data = get_data()
                    data['open_category'] = select.values[0].id if len(select.values) > 0 else None
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    client.data[ctx.guild.id]["mail"] = data
                    await interaction1.response.defer()
                    await interaction.message.edit(embed=setupmailEmbed() , view=view)
                select.callback = update_o_category
                await interaction.response.send_message("Select Mail Open Category" , view=discord.ui.View().add_item(select) , ephemeral = True)
            elif interaction.data["values"][0] == "close_category" :
                select = discord.ui.ChannelSelect(channel_types=[discord.ChannelType.category], placeholder="Mail Close Category", min_values=0, max_values=1 , default_values=[ ctx.guild.get_channel(get_data()["close_category"])] if get_data()["close_category"] else [])
                async def update_c_category(interaction1):
                    data = get_data()
                    data['close_category'] = select.values[0].id if len(select.values) > 0 else None
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    client.data[ctx.guild.id]["mail"] = data
                    await interaction1.response.defer()
                    await interaction.message.edit(embed=setupmailEmbed() , view=view)
                select.callback = update_c_category
                await interaction.response.send_message("Select Mail Close Category" , view=discord.ui.View().add_item(select), ephemeral = True)
            elif interaction.data["values"][0] == "log" :
                select = discord.ui.ChannelSelect(channel_types=[discord.ChannelType.text], placeholder="Mail Log Channel", min_values=0, max_values=1 , default_values= [ ctx.guild.get_channel(get_data()["log"])] if get_data()["log"] else [])
                async def update_log(interaction1):
                    data = get_data()
                    data['log'] = select.values[0].id if len(select.values) > 0 else None
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    client.data[ctx.guild.id]["mail"] = data
                    await interaction1.response.defer()
                    await interaction.message.edit(embed=setupmailEmbed() , view=view)
                select.callback = update_log
                await interaction.response.send_message("Select Mail Log Channel" , view=discord.ui.View().add_item(select), ephemeral = True)
            
        view = discord.ui.View()
        
        select = discord.ui.Select(placeholder="Change Settings", min_values=1, max_values=1, )
        select.add_option(label="Toggle Status", value="status" , emoji= "🔴")
        select.add_option(label="Set Channel Template", value="name" , emoji= "📒")
        select.add_option(label="Set Defult Messages", value="messages" , emoji= "📩")
        select.add_option(label="Set Support Roles", value="support_roles" , emoji= "👥")
        select.add_option(label="Set Additional Roles", value="additional_roles" , emoji= "👥")
        select.add_option(label="Set Open Category", value="open_category" , emoji= "📂")
        select.add_option(label="Set Close Category", value="close_category" , emoji= "🗄️")
        select.add_option(label="Set Log Channel", value="log" , emoji= "🗃️")
        select.callback = select_callback
        view.add_item(select)
        
        select_option = discord.ui.Select(placeholder="Manage Options", min_values=1, max_values=1, )
        select_option.add_option(label="Add Option", value="add_option" , emoji= "<:add:1188641489392238662>")
        for i , option in  enumerate(get_data()["options"]):
            select_option.add_option(label=f"{i}. {option}", value= option , emoji= get_emoji(get_data()["options"][option]['emoji']) )
        view.add_item(select_option)
        
        async def select_option_callback(interaction) :

            if interaction.user != ctx.author:
                await interaction.response.send_message("Not Your Interaction" , ephemeral = True)
                return
        
            if interaction.data["values"][0] == "add_option" :
                modal = discord.ui.Modal( title="Add Option" , timeout=180)
                modal.add_item(discord.ui.TextInput(label="Option Name", placeholder="Option Name"))
                async def on_submit(interaction):
                    data = get_data()
                    data["options"][modal.children[0].value] = { **defult_option}
                    client.data[ctx.guild.id]["mail"] = data 
                    await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                    select_option.add_option(label=f"{len(data['options'])-1}. {modal.children[0].value}", value= modal.children[0].value , emoji= get_emoji(data['options'][modal.children[0].value]['emoji']) )
                    await interaction.response.edit_message(embed=setupmailEmbed() , view=view)
                modal.on_submit = on_submit
                await interaction.response.send_modal(modal)
            else :
                option = interaction.data["values"][0]
                select_option_edit = discord.ui.Select(placeholder="Edit Option", min_values=1, max_values=1, )
                select_option_edit.add_option(label="Edit description,Emoji,Eessages", value="edit_option" , emoji= "📝")
                select_option_edit.add_option(label="Edit Support Roles", value="edit_s_roles" , emoji= "👥")
                select_option_edit.add_option(label="Delete Option", value="delete_option" , emoji= "❌")
                
                async def select_option_edit_callback(inter) :
                    if inter.data["values"][0] == "edit_option" :
                        modal = discord.ui.Modal( title="Edit Option" , timeout=180)
                        modal.add_item(discord.ui.TextInput(label="Description", placeholder="Description" , default = get_data()["options"][option]["description"] ,required=False ))
                        modal.add_item(discord.ui.TextInput(label="Message", placeholder="Message" , default = get_data()["options"][option]["message"] , style= discord.TextStyle.paragraph ,required=False ))
                        modal.add_item(discord.ui.TextInput(label="DM Message", placeholder="DM Message" , default = get_data()["options"][option]["dm_message"] , style= discord.TextStyle.paragraph , required=False ))
                        modal.add_item(discord.ui.TextInput(label="Emoji", placeholder="Emoji" , default = get_data()["options"][option]["emoji"] , required=False ))
                        async def on_submit(modal_interaction):
                            data = get_data()
                            data["options"][option]["description"] = modal.children[0].value if modal.children[0].value != "" else None 
                            data["options"][option]["message"] = modal.children[1].value if modal.children[1].value != "" else None
                            data["options"][option]["dm_message"] = modal.children[2].value if modal.children[2].value != "" else None
                            temp = discord.PartialEmoji.from_str(modal.children[3].value)
                            emoji = None
                            if temp is not None :
                                emoji = str(temp) if temp.is_unicode_emoji() else temp.id
                            data["options"][option]["emoji"] = emoji
                            client.data[ctx.guild.id]["mail"] = data 
                            await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                            await interaction.message.edit(embed=setupmailEmbed() , view=view)
                            await modal_interaction.response.defer()

                        modal.on_submit = on_submit
                        await inter.response.send_modal(modal)
                    elif inter.data["values"][0] == "edit_s_roles":
                        select = discord.ui.RoleSelect(placeholder=" Support Role", min_values=0 , max_values=25 , default_values= [ ctx.guild.get_role(role_id) for role_id in get_data()["options"][option]["support_roles"] if ctx.guild.get_role(role_id) ])
                        async def update_s_roles(interaction1):
                            data = get_data()
                            data['options'][option]['support_roles'] = [ item.id for item in select.values ]
                            await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                            client.data[ctx.guild.id]["mail"] = data
                            await interaction1.response.defer()
                            await interaction.message.edit(embed=setupmailEmbed() , view=view)
                        select.callback = update_s_roles
                        await inter.response.send_message("Select Support Roles" , view=discord.ui.View().add_item(select) , ephemeral = True)
                    elif inter.data["values"][0] == "delete_option" :
                        data = get_data()
                        data["options"].pop(option)
                        client.data[ctx.guild.id]["mail"] = data 
                        await client.db.execute('UPDATE guilds SET mail = $1 WHERE id = $2', data, ctx.guild.id)
                        await interaction.message.edit(embed=setupmailEmbed() , view=view)
                        await inter.response.edit_message(embed = discord.Embed( color = discord.Color.green() ,  description= f"Option **{option}** has been deleted") , view=None)
                select_option_edit.callback = select_option_edit_callback
                
                await interaction.response.send_message( embed = bembed(f"Edit **{option}**") , view =  discord.ui.View().add_item(select_option_edit) , ephemeral = True )
                
        select_option.callback = select_option_callback
        
        await ctx.send(embed=setupmailEmbed(), view=view)

    async def openMail(user , guild , option = None , dm = True):
        if user.id in [ Mail.mails[i] for i in Mail.mails ] :
            return 
        
        overwrites = { guild.default_role : discord.PermissionOverwrite( view_channel = False , send_messages = False ) ,
                      guild.me : discord.PermissionOverwrite( view_channel = True , send_messages = True) }
        if not dm :
            overwrites[user] = discord.PermissionOverwrite( view_channel = True , send_messages = True , attach_files = True )
        
        for role_id in client.data[guild.id]['mail']['additional_roles'] :
            if guild.get_role(role_id) :
                overwrites[guild.get_role(role_id)]  = discord.PermissionOverwrite( view_channel = True , send_messages = True , attach_files = True )
                
        for role_id in client.data[guild.id]['mail']['support_roles'] :
            if guild.get_role(role_id) :
                overwrites[guild.get_role(role_id)]  = discord.PermissionOverwrite( view_channel = True , send_messages = True , attach_files = True )
        
        if option is not None :
            for role_id in client.data[guild.id]['mail']['options'].get(option , {}).get('support_roles' , []) :
                if guild.get_role(role_id) :
                    overwrites[guild.get_role(role_id)]  = discord.PermissionOverwrite( view_channel = True , send_messages = True , attach_files = True )

        local_id = (await client.db.fetchval('SELECT MAX(local_id) FROM mails WHERE guild_id = $1' , guild.id ) or 0) + 1
        
        name = (client.data[guild.id]['mail']['name']).replace("{no}" , str(local_id)).replace("{name}" , user.name).replace("{category}" , str(option or 'Mail'))
        channel = await guild.create_text_channel( name = name[:25] , overwrites = overwrites , category= guild.get_channel( client.data[guild.id]['mail']['open_category'] if client.data[guild.id]['mail']['open_category'] else 123 ) , topic = option , reason = f"Mail Opened By {user.name}" )
        
        if dm :
            Mail.mails[channel.id] = user.id
            Mail.claims[channel.id] = []
        await client.db.execute('INSERT INTO mails (channel_id, user_id, local_id, claims , dm , guild_id , option ) VALUES ($1, $2, $3, $4, $5 , $6 , $7 )',channel.id, user.id, local_id, [] , dm , guild.id , option)
        
        user = guild.get_member(user.id)
        embed = discord.Embed(color= embed_color , description = user.mention, timestamp = datetime.now())
        embed.set_author(name = user, icon_url = user.display_avatar)
        embed.add_field(name = "Joined", value = f"- <t:{int(user.joined_at.timestamp())}:F>\n- <t:{int(user.joined_at.timestamp())}:R>")
        embed.add_field(name = "Registered", value = f"- <t:{int(user.created_at.timestamp())}:F>\n- <t:{int(user.created_at.timestamp())}:R>")
        roles = " "
        for x in reversed(user.roles):
            if x == guild.default_role :
                continue
            roles = roles + f"{x.mention} "
        embed.add_field(name = f"Roles[{len(user.roles)}]", value = roles, inline = False)
        if user and user.voice :
            data = f"{user.voice.channel.mention} {user.voice.channel.name}\n"
            if len(user.voice.channel.members) < 10 :
                for member in user.voice.channel.members :
                    data += f"- `{member.id}` {member.mention}\n"
            embed.add_field(name="Voice Channel" , value=data , inline=False)
        if user.timed_out_until :
            embed.add_field(name="Timed Out" , value=f"Unmute <t:{int(user.timed_out_until.timestamp())}:R>\n" , inline=False)

        embed.set_footer(text = f"{user.id}")
        embed.set_thumbnail(url = user.display_avatar)
        dm_message =  client.data[guild.id]['mail']['dm_message']
        message =  client.data[guild.id]['mail']['message']
        if option is not None :
            if  client.data[guild.id]['mail']['options'].get(option , {}).get('dm_message' , None) :
                dm_message =  client.data[guild.id]['mail']['options'].get(option , {}).get('dm_message' , dm_message)
            if  client.data[guild.id]['mail']['options'].get(option , {}).get('message' , None) :  
                message =  client.data[guild.id]['mail']['options'].get(option , {}).get('message' , message)
        if dm :
            view = DmManageView()
        else :
            view = ManageView()
        msg = await channel.send( message , embed=embed , view = view)
        if dm :
            # await user.send( embed= bembed(f"**{dm_message}** \n\n!close - close the Mail"))
            await user.send( embed= bembed(f"**{dm_message}**"))
        try :
            await msg.pin()
        except:
            pass
        return channel

    @commands.hybrid_command(aliases= ["pannel" , "ticket-pannel"  , "ticket" ])
    @commands.guild_only()
    @commands.has_permissions(manage_guild = True)
    async def ticketpannel(self , ctx , title = None , description = None , button_lable = None , emoji : discord.PartialEmoji = None ):
        await ctx.defer()
        embed = discord.Embed(title = title or "Mail Support" , description = description or "Click Button to create a Ticket" , color= embed_color)
        view = PannelView(lable= button_lable , emoji= emoji)
        await ctx.channel.send(embed = embed , view = view)
        
    @commands.Cog.listener()
    async def on_message(self , message):
        if message.author.bot :
            return

        if message.guild is None and message.author.id in [ Mail.mails[i] for i in Mail.mails ] :
            # send the message in the Mail 
            if message.content.startswith(defult_prefix) :
                return
            channel_id = next(key for key, value in Mail.mails.items() if value == message.author.id)
            
            channel = client.get_channel(channel_id)
            if channel is None :
                Mail.mails.pop(channel_id)
                Mail.claims.pop(channel_id)
                await  client.db.execute('DELETE FROM mails WHERE channel_id = $1', channel_id)
                return
            
            hook = None
            webhooks = await channel.webhooks() 
            for webhook in webhooks :
                if webhook.name == client.user.name :
                    hook = webhook
                    break
            
            files = []
            
            if message.attachments and len(message.attachments) > 0 :
                for attachment in message.attachments :
                    try :
                        files.append( await attachment.to_file() )
                    except :
                        files = [ await attachment.to_file() ]
            try :
                if hook is None :
                        hook = await channel.create_webhook(name = client.user.name)
                if message.stickers :
                    raise Exception
                await hook.send(content = message.content , files = files , username = message.author.name , avatar_url = message.author.display_avatar.url if message.author.display_avatar else None , allowed_mentions= discord.AllowedMentions(everyone=False, users=True, roles=False, replied_user=False))
            except Exception as e :
                await channel.send(message.content , files = files, stickers= message.stickers , allowed_mentions= discord.AllowedMentions(everyone=False, users=True, roles=False, replied_user=False) )
            
            if channel.id not in Mail.pings :
                Mail.pings[channel.id] = { "time" : 0 , "stage" : 0 }

        elif message.guild is None :
            
            if message.content.startswith(defult_prefix) :
                return
            mail_guilds = [ guild.id for guild in message.author.mutual_guilds if ( client.data[guild.id]['mail'] and  client.data[guild.id]['mail']['status']) ]
            if len(mail_guilds) == 0 :
                pass
            # elif len(mail_guilds) == 1 :
            #     view = Confirm()
            #     await message.author.send( embed = discord.Embed( description=f"Do You Want To Open A Mail In **{(client.get_guild(mail_guilds[0])).name}**" , color= embed_color ) , view = view)
            #     await view.wait()
            #     if view.value :
            #         await  openMail( message.author , client.get_guild(mail_guilds[0]) )
            else :
                if len(mail_guilds) == 1 :
                    guild = client.get_guild(mail_guilds[0])
                else :
                    view = View()
                    view.value = None
                    embed = discord.Embed(title = "Select Server" , color= embed_color)
                    embed.description = ""
                    options = [ ]
                    for i , guild_id in enumerate(mail_guilds , 1) :
                        guild = client.get_guild(guild_id)
                        embed.description += f"{i}. **{guild.name}**\n\n"
                        options.append( discord.SelectOption( label=guild.name , value = guild_id ))
                    async def select_callback(interaction) :
                        view.value = int(select.values[0])
                        await interaction.response.defer() 
                        view.stop()
                    select = Select(placeholder="Choose a server", min_values=1, max_values=1, options=options)
                    select.callback = select_callback
                    view.add_item(select)
                    
                    await message.author.send( embed = embed , view = view)
                    await view.wait()
                    
                    if view.value : 
                        guild = client.get_guild(view.value)
                    else :
                        return
                if len(self.client.data[guild.id]['mail']['options']) == 0 :  
                    view = Confirm()
                    await message.author.send( embed = discord.Embed( description=f"Do You Want To Open A Mail In **{guild.name}**" , color= embed_color ) , view = view)
                    await view.wait()
                    if view.value :
                        await Mail.openMail( message.author , guild )
                else :
                    view1 = View()
                    view1.value = None
                    embed = discord.Embed( description= f"Do You Want To Open A Mail In **{guild.name}** , Select Your Mail Reason" , color= embed_color)
                    select = discord.ui.Select(placeholder="Choose a Option", min_values=1, max_values=1)
                    for i , option in enumerate(self.client.data[guild.id]['mail']['options']) :
                        select.add_option(  label=option , description= self.client.data[guild.id]['mail']['options'][option]['description'] , emoji= get_emoji(self.client.data[guild.id]['mail']['options'][option]['emoji']) )
                        
                    async def select_callback(interaction) :
                        view1.value = select.values[0]
                        await interaction.response.defer() 
                        view1.stop()
                        
                    select.callback = select_callback
                    view1.add_item(select)
                    cancel = discord.ui.Button(label="Cancel" )
                    async def cancel_callback(interaction) :
                        await interaction.response.defer() 
                        view1.stop()
                    cancel.callback = cancel_callback
                    view1.add_item(cancel)
                    await message.author.send( embed = embed , view = view1)
                    await view1.wait()
                    
                    if view1.value is not None : 
                        await Mail.openMail( message.author , guild , option = view1.value )
        
        
        elif message.channel.id in Mail.mails :

            # if message.channel.id in Mail.pings :
            #     # pop out the id from the list
            #     Mail.pings.pop(message.channel.id)
            Mail.pings.pop(message.channel.id, None) 

            if message.content.startswith(guild_prefix(message.guild.id)) :
                return
            
            files = [] 
            if message.attachments and len(message.attachments) > 0 :
                for attachment in message.attachments :
                    try :
                        files.append( await attachment.to_file() )
                    except :
                        files = [ await attachment.to_file() ]

            if len(Mail.claims[message.channel.id]) == 0 or message.author.id in Mail.claims[message.channel.id] :
                    try :
                        await message.guild.get_member(Mail.mails[message.channel.id]).send(message.content , files = files , stickers = message.stickers)
                        await message.add_reaction("✅")
                    except :
                        await message.add_reaction("❌")

                
    @commands.hybrid_command()
    @commands.guild_only()
    async def claim(self, ctx ): 
        if ctx.channel.id not in Mail.mails :
            return 
        if ctx.channel.id in Mail.claims and ctx.author.id in Mail.claims[ctx.channel.id] :
                await ctx.send(embed = bembed(f"You Already Claimed This Mail\n- {guild_prefix(ctx.guild.id)}unclaim - To unclaim This Mail"))
                return
        role_ids = list(client.data[ctx.guild.id]['mail']['support_roles'])
        if ctx.channel.topic is not None :
            role_ids.extend(client.data[ctx.guild.id]['mail']['options'].get(ctx.channel.topic , {}).get('support_roles' , []))
        if (not bool(set(role_ids) & (set([ role.id for role in ctx.author.roles ])))) and not ctx.author.guild_permissions.manage_guild :
            return await ctx.send(embed = bembed("You Don't Have Permission To Claim This Mail"))    
        
        Mail.claims[ctx.channel.id].append(ctx.author.id)
        await client.db.execute('UPDATE mails SET claims = $1 WHERE channel_id = $2', Mail.claims[ctx.channel.id], ctx.channel.id)
        await ctx.send(embed = bembed("You Have Claimed This Mail"))
        # await client.get_user(Mail.mails[ctx.channel.id]).send(embed = bembed(f"{ctx.author.mention} Has Claimed Your Mail"))
      
    @commands.hybrid_command()
    @commands.guild_only()
    async def unclaim(self, ctx ):
        if ctx.channel.id not in Mail.mails :
            return
        if ctx.channel.id in Mail.claims and ctx.author.id in Mail.claims[ctx.channel.id] :
            Mail.claims[ctx.channel.id].remove(ctx.author.id)
            await client.db.execute('UPDATE mails SET claims = $1 WHERE channel_id = $2', Mail.claims[ctx.channel.id], ctx.channel.id)
            await ctx.send(embed = bembed("You Have Unclaimed This Mail"))
            
                
    @commands.hybrid_command()
    async def close(self, ctx ): 
        role_ids = list(client.data[ctx.guild.id]['mail']['support_roles'])
        if ctx.channel.topic is not None :
            role_ids.extend(client.data[ctx.guild.id]['mail']['options'].get(ctx.channel.topic , {}).get('support_roles' , []))
        if not bool(set(role_ids) & set([ role.id for role in ctx.author.roles ])) and not ctx.author.guild_permissions.manage_guild :
            return
        
        mail = await client.db.fetchrow('SELECT * FROM mails WHERE channel_id = $1', ctx.channel.id)
        if mail is None :
            return
        
        value = mail['user_id']
        
        if mail['dm'] :
            del Mail.mails[ctx.channel.id]
            del Mail.claims[ctx.channel.id]
        
        await client.db.execute('DELETE FROM mails WHERE channel_id = $1', ctx.channel.id)
        link = None
        if client.data[ctx.guild.id]["mail"]['log'] and ctx.guild.get_channel(client.data[ctx.guild.id]["mail"]['log']) :
            transcript = await chat_exporter.export(channel=  ctx.channel, limit = 100 , tz_info="Asia/Kolkata")
            transcript_file = discord.File(io.BytesIO(transcript.encode()), filename=f"{ctx.channel.name}.html")
            msg = await  ctx.guild.get_channel(client.data[ctx.guild.id]["mail"]['log']).send( file=transcript_file)
            link = await chat_exporter.link(msg)
            if ctx.guild.get_member(value) :
                embed = discord.Embed( title= ctx.channel.name , description= f"- Created By : {ctx.guild.get_member(value).mention} `{ctx.guild.get_member(value).id}`\n- Closed By : {ctx.author.mention}" , color= embed_color)
                await msg.reply(embed=embed , view = View().add_item(Button(label="Transcript" , url=link)))
            else :
                embed = discord.Embed( title= ctx.channel.name , description= f"- Created By : `{value}`\n- Closed By : {ctx.author.mention}" , color= embed_color)
                await msg.reply(embed=embed , view = View().add_item(Button(label="Transcript" , url=link)))
        
        if client.data[ctx.guild.id]["mail"]['close_category'] and ctx.guild.get_channel(client.data[ctx.guild.id]["mail"]['close_category']) :
            
            if not mail['dm'] :
                
                overwrites =  ctx.channel.overwrites
                for overwrite in dict(overwrites) :
                    if type(overwrite) == discord.Member :
                        overwrites.pop(overwrite)
                
                await ctx.channel.edit(category= ctx.guild.get_channel(client.data[ctx.guild.id]["mail"]['close_category']) , overwrites = overwrites)
            else :
                await ctx.channel.edit(category= ctx.guild.get_channel(client.data[ctx.guild.id]["mail"]['close_category']) )
                
            if link :
                await ctx.channel.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {ctx.author.mention}") , view=ControlView().add_item(Button(label="Transcript" , url=link)) )              
            else :
                await ctx.send(embed=discord.Embed( color = discord.Color.green() ,  description= f"Mail has been closed by {ctx.author.mention}") , view=ControlView())
        else :
            
            if link :
                await ctx.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {ctx.author.mention}") , view= View().add_item(Button(label="Transcript" , url=link)) )
            else :
                await ctx.send( embed=discord.Embed( color = discord.Color.green() ,  description= f"Ticket will delete <t:{int(datetime.now().timestamp() +10)}:R>\n- action by {ctx.author.mention}") )
            await asyncio.sleep(10)
            await ctx.channel.delete()
            
        if mail['dm'] and ctx.guild.get_member(value) :
            await ctx.guild.get_member(value).send(embed=discord.Embed( color = discord.Color.red() ,  description= f"Your Mail Has Been Closed"))

    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def openmail(self , ctx , user : discord.User , option = None ):
        channel = await Mail.openMail(user , ctx.guild , option = option )
        if channel :
            await ctx.send(embed = bembed(f"Mail has been opened in {channel.mention}"))
        else :
            await ctx.send(embed = bembed(f"Mail has been opened"))


    @commands.hybrid_command()
    @commands.guild_only()
    @commands.check( check_app_command_permission)
    async def inactivemails(self , ctx ):
        # get all the mails which are inactive from Mail.pings
        embed = discord.Embed(title = "Unresponded Mails" , color= 0x2b2c31)
        embed.description = ""
        for channel_id in dict(Mail.pings) :
            channel = client.get_channel(channel_id)
            if channel is None :
                Mail.pings.pop(channel_id)
                continue
            embed.description += f"- {channel.mention} : {round(Mail.pings[channel_id]['time'] / 60)}min\n"
        await ctx.send(embed = embed)

async def setup(client):
   await client.add_cog(Mail(client))