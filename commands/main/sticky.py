import discord
from discord.ext import commands
from discord import app_commands
from botmain import *
from commands.utils.sticky import get_sticky_manager
from typing import Optional
import asyncio

class StickyCommands(commands.Cog):
    """Commands for managing sticky messages."""

    def __init__(self, client):
        self.client = client
        self.sticky_manager = get_sticky_manager()
        self.channel_timers = {}  # Track pending refresh timers for each channel
        self.refresh_delay = 3  # 3 seconds delay

    @commands.hybrid_group(name="stickymessage", aliases=["sticky"])
    @commands.guild_only()
    @commands.has_permissions(manage_messages=True)
    async def sticky_group(self, ctx):
        """Manage sticky messages in channels."""
        if ctx.invoked_subcommand is None:
            embed = discord.Embed(
                title="Sticky Message Commands",
                description="Use the subcommands to manage sticky messages:",
                color=embed_color
            )
            embed.add_field(
                name="📝 Create",
                value="`/stickymessage create` - Create a new sticky message",
                inline=False
            )
            embed.add_field(
                name="✏️ Edit",
                value="`/stickymessage edit` - Edit an existing sticky message",
                inline=False
            )
            embed.add_field(
                name="🗑️ Delete",
                value="`/stickymessage delete` - Delete a sticky message",
                inline=False
            )
            embed.add_field(
                name="📋 List",
                value="`/stickymessage list` - List all sticky messages",
                inline=False
            )
            embed.add_field(
                name="🔄 Refresh",
                value="`/stickymessage refresh` - Refresh a sticky message",
                inline=False
            )
            await ctx.send(embed=embed)

    @sticky_group.command(name="create")
    @app_commands.describe(
        name="Unique name for this sticky message",
        channel="Channel to create the sticky message in",
        content="Text content of the message",
        title="Title for the embed (optional)",
        description="Description for the embed (optional)",
        color="Hex color for the embed (optional, e.g., #ff0000)"
    )
    async def create_sticky(
        self,
        ctx,
        name: str,
        channel: Optional[discord.TextChannel] = None,
        content: Optional[str] = None,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: Optional[str] = None
    ):
        """Create a new sticky message."""
        if not channel:
            channel = ctx.channel

        # Validate inputs
        if not content and not title and not description:
            await ctx.send("❌ You must provide either content or embed data (title/description).", ephemeral=True)
            return

        if len(name) > 100:
            await ctx.send("❌ Name must be 100 characters or less.", ephemeral=True)
            return

        # Check if sticky message with this name already exists in the channel
        existing = await self.sticky_manager.get_sticky_messages(ctx.guild.id, channel.id)
        if any(sticky['name'] == name for sticky in existing):
            await ctx.send(f"❌ A sticky message named '{name}' already exists in {channel.mention}.", ephemeral=True)
            return

        # Create embed if title or description provided
        embed = None
        if title or description:
            embed_color_int = embed_color  # Default color
            if color:
                try:
                    # Remove # if present and convert to int
                    color_hex = color.lstrip('#')
                    embed_color_int = int(color_hex, 16)
                except ValueError:
                    await ctx.send("❌ Invalid color format. Use hex format like #ff0000", ephemeral=True)
                    return

            embed = discord.Embed(
                title=title,
                description=description,
                color=embed_color_int
            )

        # Create sticky message
        sticky_id = await self.sticky_manager.create_sticky_message(
            guild_id=ctx.guild.id,
            channel_id=channel.id,
            name=name,
            content=content,
            embed=embed,
            created_by=ctx.author.id
        )

        if sticky_id:
            await ctx.send(f"✅ Sticky message '{name}' created successfully in {channel.mention}!")
        else:
            await ctx.send("❌ Failed to create sticky message. Please try again.", ephemeral=True)

    @sticky_group.command(name="edit")
    @app_commands.describe(
        name="Name of the sticky message to edit",
        channel="Channel containing the sticky message",
        content="New text content (leave empty to keep current)",
        title="New title for the embed (leave empty to keep current)",
        description="New description for the embed (leave empty to keep current)",
        color="New hex color for the embed (leave empty to keep current)"
    )
    async def edit_sticky(
        self,
        ctx,
        name: str,
        channel: Optional[discord.TextChannel] = None,
        content: Optional[str] = None,
        title: Optional[str] = None,
        description: Optional[str] = None,
        color: Optional[str] = None
    ):
        """Edit an existing sticky message."""
        if not channel:
            channel = ctx.channel

        # Find the sticky message
        sticky_messages = await self.sticky_manager.get_sticky_messages(ctx.guild.id, channel.id)
        sticky_data = next((s for s in sticky_messages if s['name'] == name), None)

        if not sticky_data:
            await ctx.send(f"❌ No sticky message named '{name}' found in {channel.mention}.", ephemeral=True)
            return

        # Prepare embed if needed
        embed = None
        if title is not None or description is not None or color is not None:
            # Get current embed data
            current_embed = sticky_data.get('embed_data', {})

            embed_title = title if title is not None else current_embed.get('title')
            embed_desc = description if description is not None else current_embed.get('description')
            embed_color_int = current_embed.get('color', embed_color)

            if color is not None:
                try:
                    color_hex = color.lstrip('#')
                    embed_color_int = int(color_hex, 16)
                except ValueError:
                    await ctx.send("❌ Invalid color format. Use hex format like #ff0000", ephemeral=True)
                    return

            if embed_title or embed_desc:
                embed = discord.Embed(
                    title=embed_title,
                    description=embed_desc,
                    color=embed_color_int
                )

        # Update sticky message
        success = await self.sticky_manager.update_sticky_message(
            sticky_id=sticky_data['id'],
            content=content,
            embed=embed,
            updated_by=ctx.author.id
        )

        if success:
            await ctx.send(f"✅ Sticky message '{name}' updated successfully!")
        else:
            await ctx.send("❌ Failed to update sticky message. Please try again.", ephemeral=True)

    @sticky_group.command(name="delete")
    @app_commands.describe(
        name="Name of the sticky message to delete",
        channel="Channel containing the sticky message"
    )
    async def delete_sticky(
        self,
        ctx,
        name: str,
        channel: Optional[discord.TextChannel] = None
    ):
        """Delete a sticky message."""
        if not channel:
            channel = ctx.channel

        # Find the sticky message
        sticky_messages = await self.sticky_manager.get_sticky_messages(ctx.guild.id, channel.id)
        sticky_data = next((s for s in sticky_messages if s['name'] == name), None)

        if not sticky_data:
            await ctx.send(f"❌ No sticky message named '{name}' found in {channel.mention}.", ephemeral=True)
            return

        # Delete sticky message
        success = await self.sticky_manager.delete_sticky_message(sticky_data['id'])

        if success:
            await ctx.send(f"✅ Sticky message '{name}' deleted successfully!")
        else:
            await ctx.send("❌ Failed to delete sticky message. Please try again.", ephemeral=True)

    @sticky_group.command(name="list")
    @app_commands.describe(
        channel="Channel to list sticky messages for (optional)"
    )
    async def list_sticky(
        self,
        ctx,
        channel: Optional[discord.TextChannel] = None
    ):
        """List all sticky messages in the guild or a specific channel."""
        sticky_messages = await self.sticky_manager.get_sticky_messages(
            ctx.guild.id,
            channel.id if channel else None
        )

        if not sticky_messages:
            location = f"in {channel.mention}" if channel else "in this server"
            await ctx.send(f"📭 No sticky messages found {location}.")
            return

        embed = discord.Embed(
            title="📌 Sticky Messages",
            color=embed_color
        )

        if channel:
            embed.description = f"Sticky messages in {channel.mention}:"
        else:
            embed.description = "All sticky messages in this server:"

        for sticky in sticky_messages:
            channel_obj = self.client.get_channel(sticky['channel_id'])
            channel_name = channel_obj.mention if channel_obj else f"<#{sticky['channel_id']}>"

            value = f"**Channel:** {channel_name}\n"
            if sticky['content']:
                content_preview = sticky['content'][:100] + "..." if len(sticky['content']) > 100 else sticky['content']
                value += f"**Content:** {content_preview}\n"

            if sticky['embed_data']:
                embed_data = sticky['embed_data']
                if embed_data.get('title'):
                    value += f"**Embed Title:** {embed_data['title']}\n"

            value += f"**Created:** <t:{sticky['created_at']}:R>"

            embed.add_field(
                name=f"📌 {sticky['name']}",
                value=value,
                inline=False
            )

        await ctx.send(embed=embed)

    @sticky_group.command(name="refresh")
    @app_commands.describe(
        name="Name of the sticky message to refresh",
        channel="Channel containing the sticky message"
    )
    async def refresh_sticky(
        self,
        ctx,
        name: str,
        channel: Optional[discord.TextChannel] = None
    ):
        """Refresh a sticky message (delete and recreate)."""
        if not channel:
            channel = ctx.channel

        success = await self.sticky_manager.refresh_sticky_message(
            ctx.guild.id,
            channel.id,
            name
        )

        if success:
            await ctx.send(f"✅ Sticky message '{name}' refreshed successfully!")
        else:
            await ctx.send(f"❌ No sticky message named '{name}' found in {channel.mention}.", ephemeral=True)

    async def _delayed_refresh(self, channel_id: int):
        """Delayed refresh function that waits for the specified delay."""
        await asyncio.sleep(self.refresh_delay)

        # Check if this timer is still the active one for this channel
        if self.channel_timers.get(channel_id) == asyncio.current_task():
            # Remove the timer and refresh sticky messages
            self.channel_timers.pop(channel_id, None)
            await self.sticky_manager.refresh_channel_sticky_messages(channel_id)

    @commands.Cog.listener()
    async def on_message(self, message):
        """Refresh sticky messages when new messages are sent (with debouncing)."""
        if message.author.bot or not message.guild:
            return

        channel_id = message.channel.id

        # Cancel existing timer for this channel if it exists
        if channel_id in self.channel_timers:
            existing_task = self.channel_timers[channel_id]
            if existing_task and not existing_task.done():
                existing_task.cancel()

        # Start a new timer for this channel
        task = asyncio.create_task(self._delayed_refresh(channel_id))
        self.channel_timers[channel_id] = task

    def cog_unload(self):
        """Clean up timers when the cog is unloaded."""
        for task in self.channel_timers.values():
            if task and not task.done():
                task.cancel()
        self.channel_timers.clear()

    @commands.Cog.listener()
    async def on_message_delete(self, message):
        """Handle sticky message deletion."""
        if not message.guild:
            return

        # Check if the deleted message was a sticky message
        try:
            sticky_data = await self.client.db.fetchrow(
                "SELECT * FROM sticky_messages WHERE message_id = $1",
                message.id
            )

            if sticky_data:
                # Re-send the sticky message
                await self.sticky_manager._send_sticky_message(sticky_data['id'])
        except Exception as e:
            print(f"Error handling sticky message deletion: {e}")

async def setup(client):
    await client.add_cog(StickyCommands(client))
