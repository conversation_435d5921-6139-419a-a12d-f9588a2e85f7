from discord.ext import commands ,tasks
from botmain import *
from discord import AppCommandPermissionType

class Guild(commands.Cog):

    def __init__(self , client):
        self.client = client
        self.load_guilds.start()

    @tasks.loop( seconds=10 , count=1)
    async def load_guilds(self):
        for guild in self.client.guilds :
            if guild.id not in self.client.data :
                try :
                    await self.client.db.execute('INSERT INTO guilds(id) VALUES ($1)' , guild.id)
                except Exception as e :
                    pass
                finally :
                    guild_data = await self.client.db.fetchrow('SELECT * FROM guilds WHERE id = $1' , guild.id)
                    client.data[guild.id] = dict(guild_data)
        
        cmd = await client.tree.fetch_commands()
        for command in cmd:
            self.client.app_command_names[command.id] = command.name
            for guild_id in self.client.app_command_settings:
                guild = client.get_guild(guild_id)
                if not guild:
                    continue
                _perms = { 'channel' : { 'allowed' : set() , 'denied' : set() , 'all_channels' : True } , 'role' : { 'allowed' : set() , 'denied' : set() , 'everyone' : False} , 'user' : { 'allowed' : set() , 'denied' : set() } }
                try :
                    data = await command.fetch_permissions(guild)
                    for perm in data.permissions:
                        if perm.type == AppCommandPermissionType.role :
                            if perm.id == guild.default_role.id :
                                _perms['role']['everyone'] = perm.permission 
                            elif perm.permission == True :
                                _perms['role']['allowed'].add(perm.id)
                            else :
                                _perms['role']['denied'].add(perm.id)
                        elif perm.type == AppCommandPermissionType.user :
                            if perm.permission == True :
                                _perms['user']['allowed'].add(perm.id)
                            else :
                                _perms['user']['denied'].add(perm.id)
                        elif perm.type == AppCommandPermissionType.channel :
                            if perm.id == guild.id -1 :
                                _perms['channel']['all_channels'] = perm.permission
                            elif perm.permission == True :
                                _perms['channel']['allowed'].add(perm.id)
                            else :
                                _perms['channel']['denied'].add(perm.id)
                    
                    self.client.app_command_settings[guild_id][command.name] = _perms
                except :
                    self.client.app_command_settings[guild_id][command.name] = _perms
        # print(self.client.app_command_settings)
        print('App Command Permissions Loaded!')

    @load_guilds.before_loop
    async def before_load_guilds(self):
        await self.client.wait_until_ready()
    
    @commands.Cog.listener()
    async def on_guild_join(self ,  guild):  
        try :
            await self.client.db.execute('INSERT INTO guilds(id) VALUES ($1)' , guild.id)
            guild_data = await self.client.db.fetchrow('SELECT * FROM guilds WHERE id = $1' , guild.id)
        except Exception as e:
            pass
        finally :
            guild_data = await self.client.db.fetchrow('SELECT * FROM guilds WHERE id = $1' , guild.id)
            self.client.data[guild.id] = dict(guild_data) 
    
    @commands.Cog.listener()
    async def on_raw_app_command_permissions_update(self,data) :
        if data.application_id != self.client.user.id :
            return
        if data.guild.id not in self.client.app_command_settings :
            return
        if data.target_id not in self.client.app_command_names :
            return
        command_name = self.client.app_command_names[data.target_id]
        for perm in data.permissions :
            if perm.type == AppCommandPermissionType.role :
                if perm.id == data.guild.default_role.id :
                    self.client.app_command_settings[data.guild.id][command_name]['role']['everyone'] = perm.permission
                elif perm.permission == True :
                    self.client.app_command_settings[data.guild.id][command_name]['role']['allowed'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['role']['denied'].discard(perm.id)
                else :
                    self.client.app_command_settings[data.guild.id][command_name]['role']['denied'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['role']['allowed'].discard(perm.id)
            elif perm.type == AppCommandPermissionType.user :
                if perm.permission == True :
                    self.client.app_command_settings[data.guild.id][command_name]['user']['allowed'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['user']['denied'].discard(perm.id)
                else :
                    self.client.app_command_settings[data.guild.id][command_name]['user']['denied'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['user']['allowed'].discard(perm.id)
            elif perm.type == AppCommandPermissionType.channel :
                if perm.id == data.guild.id -1 :
                    self.client.app_command_settings[data.guild.id][command_name]['channel']['all_channels'] = perm.permission
                elif perm.permission == True :
                    self.client.app_command_settings[data.guild.id][command_name]['channel']['allowed'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['channel']['denied'].discard(perm.id)
                else :
                    self.client.app_command_settings[data.guild.id][command_name]['channel']['denied'].add(perm.id)
                    self.client.app_command_settings[data.guild.id][command_name]['channel']['allowed'].discard(perm.id)             

async def setup(client):
   await client.add_cog(Guild(client))         
        