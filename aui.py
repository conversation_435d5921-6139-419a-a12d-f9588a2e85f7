import os
from discord.ext import commands
from botmain import client, bembed
from dotenv import load_dotenv

load_dotenv()

@client.event
async def on_ready():
    print(f'bot logged in named: {client.user}')
    user = client.get_user(591011843552837655)
    await user.send(f"{client.user} is Online Now")

@client.command()
@commands.has_permissions(manage_guild=True)
@commands.cooldown(1, 5, commands.BucketType.user)
async def ping(ctx):
    ping = round(client.latency * 1000, ndigits=2)
    await ctx.reply(embed=bembed(f'Bot: `{ping}ms`'))

client.run(os.environ.get("TOKEN"))