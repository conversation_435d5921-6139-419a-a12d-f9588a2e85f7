name: Deploy to Google Cloud VM

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.GCP_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ************ >> ~/.ssh/known_hosts

      - name: Pull latest code on VM
        run: |
          ssh -i ~/.ssh/id_rsa kamal_kishore@************ "cd /home/<USER>/AUI-BOT && git pull origin main"
